<style>
    .modal--mobile-friendly.modal--is-active{
    z-index: 9999;
}
  .newsletter-popup__content .input-group {
    flex-direction: column;
}
.newsletter-popup__content .h2::after{
  content: "";
    height: 2px;
    width: 50px;
    background: #333333;
    opacity: 1;
    display: block;


  }
  .modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded{
    background: #FCF7F2;
  }
.newsletter-popup__content .h2{
    line-height: 1.2;
    font-weight: 400;
    font-style: Regular;
    font-size: 26px;
    leading-trim: NONE;
    line-height: 35px;
    letter-spacing: 0px;
    font-family: Playfair Display;
    font-weight: 400;
    font-style: Regular;
  
  }
 .newsletter-popup__content .enlarge-text h6{
    line-height: 1.2 !important;
    font-weight: 400;
    font-style: Regular;
    font-size: 16px;
    leading-trim: NONE;
    line-height: 100%;
    letter-spacing: 0px;
    color: #666666;
  }
  .newsletter-popup__content .enlarge-text p{
    font-weight: 400;
    font-style: Regular;
    font-size: 16px;

    line-height: 22px;
    letter-spacing: 0px;
  }
  .input-group.newsletter__input-group input.newsletter__input{
    margin-bottom: 10px;
  }
  .newsletter__input-group input.newsletter__input{
    max-width: 300px;
    height: 44px;
    angle: 0 deg;
    opacity: 1;
    border-radius: 60px !important;
    border-width: 1px;
    padding: 13px 16px;
    color: #6D4C41;

  }
  .newsletter__input-group input.newsletter__input::placeholder{
    color: #999999;
  
    font-weight: 400;
    font-style: Regular;
    font-size: 16px;
   
    line-height: 100%;
    letter-spacing: 0px;


  }
  .newsletter__input-group .input-group-btn button.buttom{
    max-width: 300px;
    width:100%;
    height: 44px;
    angle: 0 deg;
    opacity: 1;
    gap: 10px;
    border-radius: 60px;
  
    font-size: 18px;
    background: #6D4C41;
    border-radius: 60px !important;
    color:#F3E8DD;
    line-height: 1;
  }
  .newsletter__input-group .input-group-btn button.buttom:hover{
    background: #502212;
  }
  .newsletter__input-group input.newsletter__input:focus,.newsletter__input-group input.newsletter__input:active,.newsletter__input-group input.newsletter__input:focus-visible{
    background: #fff;
    border: 1px solid #6D4C41;
    border-color: #6D4C41;
    outline: none;
  }
  .newsletter-popup .social-icons{
    display: flex !important;
    text-align: center;
    
    justify-content: center;
  }

  .newsletter-popup .toolbar__social a{
    padding: 0 !important;
  }

  .modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded .newsletter-popup__content{
 
    text-align: left;
  }
  .newsletter-popup .toolbar__social .icon:hover path{
    fill: #6D4C41;
  }
  @media only screen and (min-width: 881px) {
    .modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded .newsletter-popup{
      max-width: 880px;
        width: 880px;
    }
    .newsletter-popup .social-icons li{
      margin: 60px 0 0  !important;
    }
    .modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded .newsletter-popup__content{
    max-width: 300px;
    text-align: left;
    margin: 90px 82px 120px 62px;
    padding:0px;
  }
  .newsletter__input-group{
    margin: 0 auto 0 ;
  }
  newsletter-popup .modal__close{
    right: 20px;
    top: 20px;
    background: unset;
  }
  .modal__close:hover {
    background: unset;
  }
  newsletter-popup .modal__close .close-pop{
    width: 24px !important;
    height: 24px !important;
  }
  newsletter-popup .modal__close .close-pop:hover path{
    stroke: #6D4C41;
  }
  newsletter-popup .modal__close svg:not(.close-pop){
    display: none;
  }
  .newsletter-popup__content .h2::after{
    margin-top: 30px;
  }
  .newsletter-popup .rte{
    margin-top: 30px;
  }
  .newsletter-popup__content .enlarge-text h6{
    margin-bottom: 0px;
  }
  .newsletter-popup .social-icons{
    gap:50px
  }
  .newsletter-popup .toolbar__social .icon{
    width: 20px !important;
    height: 20px !important;
  }

  }
@media only screen and (max-width: 880px) {
  .modal__inner.modal__inner--bg-image{
    max-width: 100% !important;
  }
  .newsletter__input-group input.newsletter__input{
    min-width:323px;
    max-width: 100%;
    width: 100%;
    margin:0 auto;
    height:48px
}
  .newsletter__input-group .input-group-btn button.buttom{
   min-width:323px;
   margin:0 auto;
   max-width: 100%;
    width: 100%;
    height:48px;
    line-height: 1.3;
}
.modal--mobile-friendly{
   bottom: 0 !important;
}
  .modal--mobile-friendly.modal--is-active{
   
    bottom: 0;
  
    display: block;
  }
  newsletter-popup .modal__close{
    z-index: 999;
    background: unset;
    top: 10px;
    right: 10px;
    padding: 0;
  }
  newsletter-popup .modal__close svg{
    color:#000;
    width: 20px;
    height: 20px;
  }
  .modal--mobile-friendly.modal--square .newsletter--has-image.modal__centered-content--padded .newsletter-popup__content{
    padding: 27px 26px 36px;
   
  }
  .newsletter-popup .toolbar__social .icon{
    width: 24px !important;
    height: 24px !important;
  }
  .newsletter-popup__content .h2{
    line-height: 27px;
    font-weight: 400;
  
    font-size: 20px;
   
  }

  .newsletter-popup__content .h2::after{
    margin-top: 21px;

  }
  
  .newsletter-popup__content .enlarge-text h6{
    line-height: 1.2 !important;
    margin-bottom: 2px;
    font-size: 16px;

  }
  .newsletter-popup .social-icons{
    gap: 40px;
  }
  .newsletter-popup .social-icons li{
    margin: 36px 0 0  !important;
  }
  .close-pop{
    display: none;
  }
  .newsletter__input-group{
    max-width: 100%;
  }
}
</style>
{%- render 'section-newsletter-popup' -%}

{% schema %}
{
  "name": "t:labels.popup",
  "max_blocks": 1, 
  "class": "index-section--hidden",
  "settings": [
    {
      "type": "checkbox",
      "id": "disable_for_account_holders",
      "label": "t:actions.disable_for_accounts",
      "default": true,
      "info": "t:info.not_shown_to_customers_with_accounts"
    },
    {
      "type": "range",
      "id": "popup_seconds",
      "label": "t:labels.delay",
      "default": 30,
      "min": 5,
      "max": 60,
      "step": 1,
      "unit": "sec",
      "info": "t:info.values_below_10_not_recommended"
    },
    {
      "type": "range",
      "id": "popup_days",
      "label": "t:labels.frequency",
      "default": 30,
      "info": "t:info.number_of_days_popup_reappears",
      "min": 2,
      "max": 30,
      "step": 1,
      "unit": "day"
    },
    {
      "type": "header",
      "content": "t:labels.content"
    },
    {
      "type": "text",
      "id": "popup_title",
      "label": "t:labels.heading",
      "default": "Sign up and save"
    },
    {
      "type": "richtext",
      "id": "popup_text",
      "label": "t:labels.text",
      "default": "<p>Entice customers to sign up for your mailing list with discounts or exclusive offers. Include an image for extra impact.</p>"
    },
    {
      "type": "image_picker",
      "id": "popup_image",
      "label": "t:labels.image",
      "info": "t:info.does_not_appear_on_mobile"
    },
    {
      "type": "select",
      "id": "image_position",
      "label": "t:labels.image_position",
      "default": "left",
      "options": [
        {
          "value": "left",
          "label": "t:labels.alignments.left"
        },
        {
          "value": "right",
          "label": "t:labels.alignments.right"
        }
      ]
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "t:labels.color_scheme",
      "default": "none",
      "options": [
        {
          "value": "none",
          "label": "t:labels.none"
        },
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        },
        {
          "value": "3",
          "label": "3"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "show_social_icons",
      "label": "Show social icons",
      "default": false
    },
    {
      "type": "header",
      "content": "t:labels.newsletter"
    },
    {
      "type": "paragraph",
      "content": "t:info.sign_up_creates_customer"
    },
    {
      "type": "checkbox",
      "id": "enable_newsletter",
      "label": "t:actions.enable_newsletter",
      "default": true
    },
    {
      "type": "header",
      "content": "t:labels.button"
    },
    {
      "type": "text",
      "id": "button_label",
      "label": "t:labels.button_label",
      "default": "Optional button"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:labels.button_link"
    }
  ],
  "blocks": [
    {
      "type": "header",
      "name": "t:labels.sticky_reminder",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "reminder_label",
          "label": "t:labels.reminder_label",
          "default": "Get 10% off",
          "info": "t:info.appears_when_newsletter_popup_closed"
        }
      ]
    }
  ],
  "default": {
    "settings": {},
    "blocks": [
      {
        "type": "header",
        "settings": {}
      }
    ]
  },
  "disabled_on": {
    "groups": ["footer", "header"]
  }
}
{% endschema %}
