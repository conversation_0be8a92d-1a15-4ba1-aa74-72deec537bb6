<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Best Sellers</title>
  <style>
      body {
          font-family: Arial, sans-serif;
          background: #fdf7f3;
          margin: 0;
          padding: 20px;
      }

      h2 {
          font-size: 24px;
          margin-bottom: 20px;
      }

      .carousel-container {
          display: flex;
          align-items: center;
          overflow-x: auto;
          scroll-behavior: smooth;
          gap: 20px;
      }

      .product-card {
          flex: 0 0 260px;
          background: #fff;
          border-radius: 12px;
          text-align: center;
          padding: 20px;
          position: relative;
      }

      .product-card img {
          width: 100%;
          height: auto;
          border-radius: 8px;
          transition: transform 0.3s ease;
      }

      .product-card img:hover {
          transform: scale(1.05); /* 鼠标悬停放大5% */
      }

      .product-name {
          font-size: 14px;
          margin-top: 10px;
          color: #555;
      }

      .price {
          margin: 8px 0;
          font-weight: bold;
      }

      .old-price {
          text-decoration: line-through;
          color: #999;
          margin-right: 8px;
      }

      .badge-sale {
          position: absolute;
          top: 10px;
          left: 10px;
          background: #f06435;
          color: #fff;
          font-size: 12px;
          padding: 2px 6px;
          border-radius: 4px;
      }

      .badge-discount {
          position: absolute;
          top: 10px;
          left: 50px;
          background: #ffb26b;
          color: #fff;
          font-size: 12px;
          padding: 2px 6px;
          border-radius: 4px;
      }

      .btn {
          background: #5c4033;
          color: white;
          padding: 10px 20px;
          border: none;
          border-radius: 20px;
          cursor: pointer;
          margin-top: 10px;
      }

      /* 手机端一行两个 */
      @media screen and (max-width: 768px) {
          .carousel-container {
              flex-wrap: wrap;
              justify-content: center;
          }

          .product-card {
              flex: 0 0 45%;
          }
      }
  </style>
</head>
<body>
<h2>Best Sellers</h2>
<div class="carousel-container">
  <!-- 第一个商品 -->
  <div class="product-card">
    <img src="img1.jpg" alt="Product 1">
    <div class="product-name">Savanna Shoe Cabinet, 24 Pairs Shoes</div>
    <div class="price">$199.99</div>
    <button class="btn">Shop Now</button>
  </div>

  <!-- 第二个商品 -->
  <div class="product-card">
    <img src="img2.jpg" alt="Product 2">
    <div class="product-name">Savanna Shoe Cabinet, 24 Pairs Shoes</div>
    <div class="price">$199.99</div>
    <button class="btn">Shop Now</button>
  </div>

  <!-- 第三个商品（促销中） -->
  <div class="product-card">
    <span class="badge-sale">Sale</span>
    <span class="badge-discount">-23%</span>
    <img src="img3.jpg" alt="Product 3">
    <div class="product-name">Savanna Shoe Cabinet, 24 Pairs Shoes</div>
    <div class="price">
      <span class="old-price">$259.99</span>
      <span>$199.99</span>
    </div>
    <button class="btn">Shop Now</button>
  </div>

  <!-- 第四个商品 -->
  <div class="product-card">
    <img src="img4.jpg" alt="Product 4">
    <div class="product-name">Savanna Shoe Cabinet, 24 Pairs Shoes</div>
    <div class="price">$199.99</div>
    <button class="btn">Shop Now</button>
  </div>
</div>
</body>
</html>
