<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Savanna Collection</title>
<!--  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400&display=swap" rel="stylesheet">-->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
  <style>
      * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
      }

      body {
          font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
          line-height: 1.6;
      }

      .container {
          width: 100%;
          margin: 0 auto;
          background-color: white;
      }

      /* 面包屑导航 */
      .breadcrumb {
          padding: 0 0 20px 100px;
          font-size: 16px;
          font-weight: 400;
          background-color: white;
      }

      .breadcrumb a {
          color: #999999;
          text-decoration: none;
      }

      .breadcrumb a:hover {
          text-decoration: underline;
      }

      .breadcrumb .separator {
          color: #999999;
          margin: 0 8px;
      }

      .breadcrumb .current {
          color: #666666;
      }

      /* 横幅区域 */
      .banner {
          position: relative;
          height: 60vh;
          min-height: 400px;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
      }

      .banner-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
      }

      .swiper {
          width: 100%;
          height: 760px;
      }

      .swiper-slide {
          text-align: center;
          font-size: 18px;
          background: #444;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
      }

      .swiper-slide img {
          display: block;
          width: 100%;
          height: 100%;
          object-fit: cover;
      }

      .swiper-slide video {
          width: 100%;
          height: 100%;
          object-fit: cover;
      }

      /* 幻灯片内容区域样式 */
      .slide-content {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.5);
          padding: 20px;
          text-align: left;
      }

      /* 幻灯片标题样式 */
      .slide-title {
          font-size: 24px;
          margin-bottom: 10px;
      }


      /* 内容区域 */
      /*.content {*/
      /*    padding: 77px 340px 99px 338px;*/
      /*    display: flex;*/
      /*    align-items: flex-start;*/
      /*    gap: 70px;*/
      /*    color: #8F8884;*/
      /*}*/

      /*.content-logo {*/
      /*    flex-shrink: 0;*/
      /*    display: flex;*/
      /*    align-items: center;*/
      /*    justify-content: center;*/
      /*}*/

      /*.logo-text {*/
      /*    font-family: Lato, sans-serif;*/
      /*    font-size: 100px;*/
      /*    font-weight: 250;*/
      /*    color: #8F8884;*/
      /*}*/

      /*.content-box {*/
      /*    flex: 1;*/
      /*}*/

      /*.content-line {*/
      /*    background: #8F8884;*/
      /*    height: 1px;*/
      /*    width: 80px;*/
      /*    margin-bottom: 20px;*/
      /*}*/

      /*.content-text {*/
      /*    font-size: 16px;*/
      /*    font-weight: 400;*/
      /*    line-height: 30px;*/
      /*}*/


      /* 内容区域 */
      .content-1 {
          padding: 70px 340px 90px 338px;
          color: #6D4C41;
          background: #FCF7F2;
      }

      .logo-text {
          color: #6D4C41;
          font-family: Playfair Display;
          font-weight: 400;
          font-size: 46px;
            letter-spacing: 0px;
      }

      .content-box {
          flex: 1;
      }

      .content-line {
          background: #6D4C41;
          height: 1px;
          width: 80px;
          margin-bottom: 20px;
      }

      .content-text {
          font-family: PingFang SC;
          font-weight: 400;
          font-size: 16px;
          line-height: 30px;
          letter-spacing: 0px;
      }



      /* 特色区域容器 */
      .feature-section {
          width: 100%;
          display: flex;
          flex-direction: column;
          gap: 80px;
          margin-bottom: 80px;
      }
      .feature-section-p {
          gap: 95px;
          margin-bottom: 64px;
      }

      /* 特色内容区域 */
      .feature-content {
          display: flex;
          flex-direction: row;
          flex-wrap: wrap;
          justify-content: space-between;
      }

      /* 特色图片 */
      .feature-image {
          flex: 1 1 65%;
          max-height: 459px;
          overflow: hidden;
          position: relative;

      }

       .feature-image-height {
          max-height: 600px ;
      }

      .feature-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
      }

      .feature-image img:hover {
          transform: scale(1.05);
      }

      /* 特色文字内容 */
      .feature-text {
          flex: 1 1 30%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding: 60px;
          color: #ffffff;
          background: #C8A983;
      }

      .feature-text h2 {
          font-family: 'Playfair Display', serif;
          font-weight: 400;
          font-size: 26px;
          line-height: 100%;
          margin-bottom: 20px;
      }

      .feature-text p {
          font-family: 'PingFang SC', sans-serif;
          font-weight: 400;
          font-size: 16px;
          line-height: 30px;
      }

      /* 产品展示区域 */
      .product-showcase {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          padding: 0 100px;
          gap: 20px;
      }

      .product-item {
          flex: 1 1 45%;
          position: relative;
          overflow: hidden;
          max-width: 820px;
          aspect-ratio: 1 / 1;
      }

      .product-item img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
      }

      .product-item img:hover {
          transform: scale(1.05);
      }

      /* 产品按钮 */
      .product-button {
          position: absolute;
          bottom: 20px;
          right: 0;
          background-color: rgba(255, 255, 255, 0.7);
          padding: 15px 25px;
          font-family: 'Playfair Display', serif;
          font-weight: 400;
          font-size: 26px;
          line-height: 100%;
          border: none;
          cursor: pointer;
          transition: background-color 0.3s ease;
          border-radius: 50px 0 0 50px;
      }

      .product-button:hover {
          background-color: rgba(255, 255, 255, 1);
      }

      /* 产品网格标题 */
      .grid-title {
          font-family: 'Playfair Display', serif;
          font-weight: 400;
          font-size: 26px;
          margin-bottom: 29px;
          margin-left: 100px;
      }

      /* 产品网格容器 */
      .product-grid {
          display: grid;
          grid-template-columns: repeat(5, 1fr);
          gap: 20px;
          padding: 0 90px;
          margin-bottom: 50px;
      }

      /* 产品卡片 */
      .product-card {
          text-align: center;
          margin-bottom: 20px;
      }

      .product-card img {
          width: 100%;
          height: 328px;
          object-fit: cover;
          transition: transform 0.3s ease;
      }

      .product-card img:hover {
          transform: scale(1.05);
      }

      /* 价格容器 */
      .price-box {
          margin: 10px 0;
          font-family: 'PingFang SC', sans-serif;
          font-weight: 500;
          font-size: 14px;
      }

      .price-original {
          text-decoration: line-through;
          color: #999999;
          margin-right: 5px;
      }

      .price-sale {
          color: #EB5E30;
      }

      .price-normal {
          color: #333333;
      }

      /* 购买按钮 */
      .shop-button {
          display: inline-block;
          width: 147px;
          height: 38px;
          border-radius: 60px;
          border: 1px solid #5C3D2E;
          background-color: white;
          color: #5C3D2E;
          cursor: pointer;
          font-family: 'PingFang SC', sans-serif;
          transition: all 0.3s ease;
      }

      .shop-button:hover {
          background-color: #5C3D2E;
          color: white;
      }

      /* 响应式设计 - 平板设备 */
      @media (max-width: 1024px) {
          .content {
              padding: 40px 100px;
              gap: 40px;
          }

          .product-showcase {
              padding: 0 50px;
          }

          .product-grid {
              grid-template-columns: repeat(4, 1fr);
              padding: 0 50px;
          }

          .grid-title {
              margin-left: 50px;
          }
      }

      /* 响应式设计 - 手机设备 */
      @media (max-width: 768px) {
          .breadcrumb {
              padding: 15px 20px;
              font-size: 14px;
          }

          .banner {
              height: 50vh;
              min-height: 300px;
          }

          .content {
              flex-direction: column;
              padding: 40px 20px;
              gap: 30px;
          }

          .content-logo {
              width: 100%;
              justify-content: flex-start;
          }

          .logo-text {
              font-size: 32px;
              letter-spacing: 4px;
          }

          .content-text {
              font-size: 14px;
              line-height: 26px;
          }

          .feature-content {
              flex-direction: column;
          }

          .feature-image {
              height: 300px;
              margin-bottom: 20px;
          }

          .feature-text {
              padding: 30px 20px;
          }

          .feature-text h2 {
              font-size: 22px;
              margin-bottom: 15px;
          }

          .feature-text p {
              font-size: 14px;
              line-height: 26px;
          }

          .product-showcase {
              flex-direction: column;
              padding: 0 20px;
          }

          .product-item {
              margin-bottom: 20px;
          }

          .product-button {
              font-size: 20px;
              padding: 12px 20px;
          }

          .grid-title {
              margin-left: 20px;
              font-size: 22px;
          }

          .product-grid {
              grid-template-columns: repeat(2, 1fr);
              padding: 0 20px;
              gap: 15px;
          }

          .product-card img {
              height: 250px;
          }
      }
  </style>
</head>
<body>
<div class="container">
  <!-- 面包屑导航 -->
  <nav class="breadcrumb">
    <a href="#" onclick="goHome()">首页</a>
    <span class="separator">·</span>
    <span class="current">Savanna Collection</span>
  </nav>

  <!-- 横幅区域 -->
<!--  <section class="banner">-->
<!--    <img src="" alt="Savanna Collection" class="banner-image" id="bannerImage">-->
<!--  </section>-->

  <div class="swiper mySwiper">
    <div class="swiper-wrapper" id="swiper-wrapper"></div>
<!--    <div class="swiper-button-next"></div>-->
<!--    <div class="swiper-button-prev"></div>-->
<!--    <div class="swiper-pagination"></div>-->
  </div>


  <!-- 内容区域 -->
  <section class="content-1">
    <div class="content-logo">
      <div class="logo-text" id="logoText">Savanna</div>
    </div>
    <div class="content-box">
      <div class="content-line"></div>
      <div class="content-text" id="mainDescription">
        <p>对于那些希望在室内引入更多纹理和视觉趣味的人来说，Cas系列以其波浪形面板而闻名，为任何空间增添深度和层次感。虽然波浪图案可能会产生大胆的视觉效果，但家具采用微妙的冷色调木材，从白色到深灰色桤木，让人感觉相当宁静。该系列以仙后座命名，这是一个代表好运、愿望实现、善良和纯洁的星座——所有这些都是您想要融入家中的元素！</p>
      </div>
    </div>
  </section>

  <!-- 内容区域 -->
  <section class="content">
    <div class="content-logo">
      <div class="logo-text" id="logoText">Cas</div>
    </div>
    <div class="content-box">
      <div class="content-line"></div>
      <div class="content-text" id="mainDescription">
        <p>对于那些希望在室内引入更多纹理和视觉趣味的人来说，Cas系列以其波浪形面板而闻名，为任何空间增添深度和层次感。虽然波浪图案可能会产生大胆的视觉效果，但家具采用微妙的冷色调木材，从白色到深灰色桤木，让人感觉相当宁静。该系列以仙后座命名，这是一个代表好运、愿望实现、善良和纯洁的星座——所有这些都是您想要融入家中的元素！</p>
      </div>
    </div>
  </section>

  <!-- 特色区域 -->
  <div class="feature-section">
    <!-- 第一个特色区域 -->
    <div class="feature-content">
      <div class="feature-image">
        <img src="" alt="特色展示" id="featureImage1">
      </div>
      <div class="feature-text">
        <h2 id="featureTitle1">为什么您会喜欢它：</h2>
        <p id="featureDescription1">
          由可持续采伐的桤木雕刻而成，这个家具系列体现了天体的宁静。木材的微妙纹理如星尘般流淌，而哑光饰面反射着仙后座的低调光芒——因为真正的优雅从不张扬。
        </p>
      </div>
    </div>
    <!-- 产品展示区域 -->
    <div class="product-showcase">
      <div class="product-item">
        <img src="" alt="书架" id="productImage1">
        <button class="product-button" onclick="viewProduct('bookcases')">书架 →</button>
      </div>
      <div class="product-item">
        <img src="" alt="咖啡桌" id="productImage2">
        <button class="product-button" onclick="viewProduct('coffee-tables')">咖啡桌 →</button>
      </div>
    </div>
  </div>

  <!-- 第二个特色区域 -->
  <div class="feature-section feature-section-p">
    <div class="feature-content">
      <div class="feature-text">
        <h2 id="featureTitle2">工艺精湛：</h2>
        <p id="featureDescription2">
          每件家具都经过精心制作，注重细节和品质。我们使用传统工艺结合现代设计理念，确保每件产品都能经受时间的考验，同时保持其独特的美学魅力。
        </p>
      </div>
      <div class="feature-image feature-image-height">
        <img src="" alt="工艺展示" id="featureImage2">
      </div>
    </div>

    <!-- 第二个产品展示区域 -->
    <div class="product-showcase">
      <div class="product-item">
        <img src="" alt="储物柜" id="productImage3">
        <button class="product-button" onclick="viewProduct('storage')">储物柜 →</button>
      </div>
      <div class="product-item">
        <img src="" alt="边桌" id="productImage4">
        <button class="product-button" onclick="viewProduct('side-tables')">边桌 →</button>
      </div>
    </div>
  </div>

  <!-- 产品网格标题 -->
  <h2 class="grid-title" id="gridTitle">热门推荐</h2>

  <!-- 产品网格区域 -->
  <div class="product-grid" id="productGrid">
    <!-- 商品项由 JavaScript 动态生成 -->
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
<script>
  // 配置对象 - 所有可配置的内容
  const config = {
    // 文本内容配置
    text: {
      logoText: "Cas",
      mainDescription: 'For those looking to introduce a bit more texture and visual intrigue into their interiors, the Cas Collection is notable for its waveform panels that add depth and dimension to any space. While the wavy pattern might make a bold statement, the furniture comes in subtle cool-toned woods, from white to dark gray alder, and feels rather calming. The collection is named for Cassiopeia, a constellation representing good luck, the fulfillment of wishes, and kindness and purity—all things you want to incorporate into your home! Whether you\'re furnishing a cozy bedroom or a chic living room, Cas adds a layer of refinement without overwhelming the senses.',
      featureTitle1: "为什么您会喜欢它：",
      featureDescription1: "由可持续采伐的桤木雕刻而成，这个家具系列体现了天体的宁静。木材的微妙纹理如星尘般流淌，而哑光饰面反射着仙后座的低调光芒——因为真正的优雅从不张扬。",
      featureTitle2: "工艺精湛：",
      featureDescription2: "每件家具都经过精心制作，注重细节和品质。我们使用传统工艺结合现代设计理念，确保每件产品都能经受时间的考验，同时保持其独特的美学魅力。",
      gridTitle: "热门推荐"
    },

    // 图片配置
    images: {
      bannerImage: "https://gips3.baidu.com/it/u=1907453080,4211057612&fm=3028&app=3028&f=JPEG&fmt=auto&q=100&size=f1000_1000",
      featureImage1: "https://gips3.baidu.com/it/u=1907453080,4211057612&fm=3028&app=3028&f=JPEG&fmt=auto&q=100&size=f1000_1000",
      featureImage2: "https://gips3.baidu.com/it/u=1907453080,4211057612&fm=3028&app=3028&f=JPEG&fmt=auto&q=100&size=f1000_1000",
      productImage1: "https://gips3.baidu.com/it/u=1907453080,4211057612&fm=3028&app=3028&f=JPEG&fmt=auto&q=100&size=f1000_1000",
      productImage2: "https://gips3.baidu.com/it/u=1907453080,4211057612&fm=3028&app=3028&f=JPEG&fmt=auto&q=100&size=f1000_1000",
      productImage3: "https://gips3.baidu.com/it/u=1907453080,4211057612&fm=3028&app=3028&f=JPEG&fmt=auto&q=100&size=f1000_1000",
      productImage4: "https://gips3.baidu.com/it/u=1907453080,4211057612&fm=3028&app=3028&f=JPEG&fmt=auto&q=100&size=f1000_1000"
    },

    // 产品数据配置
    products: [
      {
        img: "http://gips3.baidu.com/it/u=70459541,3412285454&fm=3028&app=3028&f=JPEG&fmt=auto?w=960&h=1280",
        price: 1999.99
      },
      {
        img: "http://gips3.baidu.com/it/u=70459541,3412285454&fm=3028&app=3028&f=JPEG&fmt=auto?w=960&h=1280",
        price: 2999.99,
        originalPrice: 3999.99,
        salePrice: 2999.99
      },
      {
        img: "http://gips3.baidu.com/it/u=70459541,3412285454&fm=3028&app=3028&f=JPEG&fmt=auto?w=960&h=1280",
        price: 3899.99
      },
      {
        img: "http://gips3.baidu.com/it/u=70459541,3412285454&fm=3028&app=3028&f=JPEG&fmt=auto?w=960&h=1280",
        price: 3599.99
      },
      {
        img: "http://gips3.baidu.com/it/u=70459541,3412285454&fm=3028&app=3028&f=JPEG&fmt=auto?w=960&h=1280",
        price: 1999.99
      },
      {
        img: "http://gips3.baidu.com/it/u=70459541,3412285454&fm=3028&app=3028&f=JPEG&fmt=auto?w=960&h=1280",
        price: 1999.99
      },
      {
        img: "http://gips3.baidu.com/it/u=70459541,3412285454&fm=3028&app=3028&f=JPEG&fmt=auto?w=960&h=1280",
        price: 2999.99
      },
      {
        img: "http://gips3.baidu.com/it/u=70459541,3412285454&fm=3028&app=3028&f=JPEG&fmt=auto?w=960&h=1280",
        price: 3899.99
      },
      {
        img: "http://gips3.baidu.com/it/u=70459541,3412285454&fm=3028&app=3028&f=JPEG&fmt=auto?w=960&h=1280",
        price: 3599.99
      },
      {
        img: "http://gips3.baidu.com/it/u=70459541,3412285454&fm=3028&app=3028&f=JPEG&fmt=auto?w=960&h=1280",
        price: 1999.99
      }
    ]
  };
  const slidesConfig = [
    {
      type: 'image',
      src: 'https://source.unsplash.com/random/800x600?nature=1',
      title: 'Nature 1',
      description: 'Beautiful nature scene'
    },
    {
      type: 'video',
      src: 'https://example.com/video1.mp4',
      poster: 'https://source.unsplash.com/random/800x600?nature=2',
      title: 'Video 1',
      description: 'Amazing video content'
    },
    {
      type: 'image',
      src: 'https://source.unsplash.com/random/800x600?city=1',
      title: 'City 1',
      description: 'Urban landscape'
    },
    {
      type: 'image',
      src: 'https://source.unsplash.com/random/800x600?animal=1',
      title: 'Animal 1',
      description: 'Wildlife photography'
    },
    {
      type: 'video',
      src: 'https://example.com/video2.mp4',
      poster: 'https://source.unsplash.com/random/800x600?tech=1',
      title: 'Video 2',
      description: 'Technology showcase'
    }
  ];

  // 轮播图配置
  const swiperConfig = {
    spaceBetween: 30,      // 幻灯片间距
    centeredSlides: true,  // 居中显示幻灯片
    mousewheel: true,
    autoplay: {           // 自动播放设置
      delay: 2500,        // 切换延迟时间(毫秒)
      disableOnInteraction: false, // 用户交互后不停止自动播放
    },
    pagination: {          // 分页器设置
      el: ".swiper-pagination", // 分页器容器
      clickable: true,     // 可点击分页器切换
    },
    navigation: {          // 导航按钮设置
      nextEl: ".swiper-button-next", // 下一页按钮
      prevEl: ".swiper-button-prev", // 上一页按钮
    },
    // 事件处理
    on: {
      init: function () {
        // 初始化时暂停所有视频
        const videos = document.querySelectorAll('.swiper-slide video');
        videos.forEach(video => {
          video.pause();
        });
      },
      slideChange: function () {
        // 幻灯片切换时暂停所有视频
        const videos = document.querySelectorAll('.swiper-slide video');
        videos.forEach(video => {
          video.pause();
        });

        // 播放当前幻灯片的视频(如果有)
        const activeSlide = this.slides[this.activeIndex];
        const video = activeSlide.querySelector('video');
        if (video) {
          video.currentTime = 0; // 重置播放位置
          video.play().catch(e => console.log('视频自动播放被阻止:', e));
        }
      }
    }
  };

  /**
   * 根据配置创建幻灯片HTML
   * @param {Object} slideConfig 幻灯片配置对象
   * @returns {string} 幻灯片HTML字符串
   */
  function createSlideHTML(slideConfig) {
    let mediaHTML = ''; // 媒体内容HTML

    // 根据类型创建不同的媒体元素
    if (slideConfig.type === 'image') {
      // 图片幻灯片
      mediaHTML = `<img src="${slideConfig.src}" alt="${slideConfig.title || ''}">`;
    } else if (slideConfig.type === 'video') {
      // 视频幻灯片
      mediaHTML = `
          <video controls playsinline poster="${slideConfig.poster || ''}">
            <source src="${slideConfig.src}" type="video/mp4">
            您的浏览器不支持视频标签
          </video>
        `;
    }

    // 返回完整的幻灯片HTML
    return `
        <div class="swiper-slide">
          ${mediaHTML}

        </div>
      `;
  }

  // <div className="slide-content">
  //   <h3 className="slide-title">${slideConfig.title || ''}</h3>
  // </div>

  // 页面加载完成后初始化
  document.addEventListener('DOMContentLoaded', function () {
    // 获取幻灯片容器
    const swiperWrapper = document.getElementById('swiper-wrapper');

    // 根据配置创建并添加所有幻灯片
    slidesConfig.forEach(slide => {
      swiperWrapper.innerHTML += createSlideHTML(slide);
    });

    // 初始化Swiper
    const swiper = new Swiper(".mySwiper", swiperConfig);
  });

  // 初始化页面内容
  function initializeContent() {
    // 设置文本内容
    document.getElementById('logoText').textContent = config.text.logoText;
    document.getElementById('mainDescription').innerHTML = `<p>${config.text.mainDescription}</p>`;
    document.getElementById('featureTitle1').textContent = config.text.featureTitle1;
    document.getElementById('featureDescription1').textContent = config.text.featureDescription1;
    document.getElementById('featureTitle2').textContent = config.text.featureTitle2;
    document.getElementById('featureDescription2').textContent = config.text.featureDescription2;
    document.getElementById('gridTitle').textContent = config.text.gridTitle;

    // 设置图片
    document.getElementById('bannerImage').src = config.images.bannerImage;
    document.getElementById('featureImage1').src = config.images.featureImage1;
    document.getElementById('featureImage2').src = config.images.featureImage2;
    document.getElementById('productImage1').src = config.images.productImage1;
    document.getElementById('productImage2').src = config.images.productImage2;
    document.getElementById('productImage3').src = config.images.productImage3;
    document.getElementById('productImage4').src = config.images.productImage4;

    // 生成产品网格
    generateProductGrid();
  }

  // 生成产品网格
  function generateProductGrid() {
    const productGrid = document.getElementById("productGrid");
    productGrid.innerHTML = ''; // 清空现有内容

    config.products.forEach(product => {
      const card = document.createElement("div");
      card.className = "product-card";

      // 构建价格 HTML
      let priceHTML = '';
      if (product.salePrice && product.originalPrice && product.salePrice < product.originalPrice) {
        priceHTML = `
            <span class="price-original">¥${product.originalPrice.toFixed(2)}</span>
            <span class="price-sale">¥${product.salePrice.toFixed(2)}</span>
          `;
      } else {
        priceHTML = `<span class="price-normal">¥${product.price.toFixed(2)}</span>`;
      }

      // 商品HTML结构
      card.innerHTML = `
          <img src="${product.img}" alt="产品图片" />
          <div class="price-box">${priceHTML}</div>
          <button class="shop-button" onclick="shopNow()">立即购买</button>
        `;

      productGrid.appendChild(card);
    });
  }

  // 首页导航
  function goHome() {
    alert('正在跳转到首页...');
    // 实际应用中使用: window.location.href = '/';
  }

  // 查看产品
  function viewProduct(productType) {
    const productNames = {
      'bookcases': '书架',
      'coffee-tables': '咖啡桌',
      'storage': '储物柜',
      'side-tables': '边桌'
    };

    const productName = productNames[productType] || '产品';
    alert(`正在跳转到${productName}页面...`);
    // 实际应用中使用: window.location.href = `/products/${productType}`;
  }

  // 立即购买
  function shopNow() {
    alert('正在跳转到购物车...');
    // 实际应用中处理购买逻辑
  }

  // 页面加载完成后初始化
  document.addEventListener('DOMContentLoaded', function () {
    initializeContent();

    // 添加滚动动画效果
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = '1';
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, observerOptions);

    // 为产品卡片添加动画
    const productCards = document.querySelectorAll('.product-card');
    productCards.forEach((card, index) => {
      card.style.opacity = '0';
      card.style.transform = 'translateY(30px)';
      card.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
      observer.observe(card);
    });
  });
</script>
</body>
</html>