{%- comment -%}
  Renders a furniture showcase section with navigation and product grid.

  Supports both images and videos with separate configurations for PC and mobile:
  - Image formats: WebP (recommended), JPEG (≥80% quality), PNG (transparency), SVG (vector)
  - Video formats: MP4 (H.264), WebM (VP9), YouTube, Vimeo
  - Video auto-play: muted & loop only for optimal UX
  - Mobile-specific media: Configure different images/videos for mobile devices
  - Fallback system: Mobile uses PC media if mobile-specific media is not configured

  Accepts:
  - title {string} - The title of the section
  - heading_size {string} - The size of the heading
  - navigation items and links
  - PC images and videos (6 slides × 5 media items each)
  - Mobile images and videos (6 slides × 4 media items each)
  - button settings
  - layout settings

  Usage:
  {% render 'section-furniture-showcase' %}
{%- endcomment -%}

{%- liquid
  assign title = title | default: section.settings.title
  assign heading_size = heading_size | default: section.settings.heading_size | default: 'h2'
  assign background_color = section.settings.background_color | default: '#f8f8f8'
  assign button_label = section.settings.button_label | default: 'Read More'
  assign button_link = section.settings.button_link
  assign button_style = section.settings.button_style | default: 'primary'
  assign divider = section.settings.divider | default: false
  assign top_padding = section.settings.top_padding | default: true
  assign bottom_padding = section.settings.bottom_padding | default: true
  assign auto_play = section.settings.auto_play | default: true
  assign auto_play_speed = section.settings.auto_play_speed | default: 5

  assign nav_links = ''
  assign nav_links = nav_links | append: section.settings.nav_link_1 | append: '|'
  assign nav_links = nav_links | append: section.settings.nav_link_2 | append: '|'
  assign nav_links = nav_links | append: section.settings.nav_link_3 | append: '|'
  assign nav_links = nav_links | append: section.settings.nav_link_4 | append: '|'
  assign nav_links = nav_links | append: section.settings.nav_link_5 | append: '|'
  assign nav_links = nav_links | append: section.settings.nav_link_6
  assign nav_links_array = nav_links | split: '|'
-%}

{% style %}
  #FurnitureShowcase-{{ section.id }} {
    background-color: {{ background_color }};
    {% if top_padding %}padding-top: 64px;{% endif %}
    {% if bottom_padding %}padding-bottom: 64px;{% endif %}
  }
  
  #FurnitureShowcase-{{ section.id }} .carousel-container {
    position: relative;
    margin: 0 auto;
  }

  #FurnitureShowcase-{{ section.id }} .furniture-nav {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 30px;
    flex-wrap: wrap;
  }

  #FurnitureShowcase-{{ section.id }} .furniture-nav button {
    font-family: Playfair Display;
    font-style: Regular;
    font-size: 26px;
    line-height: 100%;
    letter-spacing: 0px;
    background: none;
    border: none;
    color: #999;
    font-weight: 400;
    transition: color 0.3s ease;
    position: relative;
    cursor: pointer;
    padding: 8px 0;
  }

  #FurnitureShowcase-{{ section.id }} .furniture-nav button:hover {
    color: #333;
  }

  #FurnitureShowcase-{{ section.id }} .furniture-nav button.active {
    color: #333;
    font-weight: 500;
  }

  #FurnitureShowcase-{{ section.id }} .furniture-nav button.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #333;
  }

  #FurnitureShowcase-{{ section.id }} .carousel-wrapper {
    overflow: hidden;
    position: relative;
  }

  #FurnitureShowcase-{{ section.id }} .carousel-track {
    display: flex;
    transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  #FurnitureShowcase-{{ section.id }} .carousel-slide {
    min-width: 100%;
  }
  
  #FurnitureShowcase-{{ section.id }} .section-title {
    font-family: PingFang SC;
    font-weight: 400;
    font-style: Regular;
    leading-trim: NONE;
    line-height: 100%;
    letter-spacing: 0px;

    text-align: center;
    margin-bottom: 10px;
    color: #999999;
    weight: 400;
    font-size: 16px;
    margin-left: auto;
    margin-right: auto;
  }
  
  #FurnitureShowcase-{{ section.id }} .furniture-grid {
    display: grid;
    grid-template-columns: 250px 1fr;
    grid-template-rows: repeat(4, 1fr);
    gap: 16px;
    margin: 0 auto 40px;
    height: 400px;
  }

  #FurnitureShowcase-{{ section.id }} .furniture-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    background-color: #fff;
  }

  /* Grid layout positioning */
  #FurnitureShowcase-{{ section.id }} .furniture-item.image-1 {
    grid-column: 1;
    grid-row: 1 / 5;
  }

  #FurnitureShowcase-{{ section.id }} .furniture-item.image-2 {
    grid-column: 2;
    grid-row: 1;
  }

  #FurnitureShowcase-{{ section.id }} .furniture-item.image-3 {
    grid-column: 2;
    grid-row: 2;
  }

  #FurnitureShowcase-{{ section.id }} .furniture-item.image-4 {
    grid-column: 2;
    grid-row: 3;
  }

  #FurnitureShowcase-{{ section.id }} .furniture-item.image-5 {
    grid-column: 2;
    grid-row: 4;
  }

  #FurnitureShowcase-{{ section.id }} .furniture-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    display: block;
  }

  #FurnitureShowcase-{{ section.id }} .furniture-item:hover img {
    transform: scale(1.05);
  }

  /* Video media styles */
  #FurnitureShowcase-{{ section.id }} .video-media {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
  }

  #FurnitureShowcase-{{ section.id }} .video-media video,
  #FurnitureShowcase-{{ section.id }} .video-media iframe {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border: none;
  }

  #FurnitureShowcase-{{ section.id }} .slide-main-image .video-media,
  #FurnitureShowcase-{{ section.id }} .slide-image-left .video-media,
  #FurnitureShowcase-{{ section.id }} .slide-image-right .video-media,
  #FurnitureShowcase-{{ section.id }} .slide-image-bottom-left .video-media,
  #FurnitureShowcase-{{ section.id }} .slide-image-bottom-right .video-media {
    width: 100%;
    height: 100%;
  }

  /* Mobile video styles */
  @media only screen and (max-width: 768px) {
    #FurnitureShowcase-{{ section.id }} .video-media {
      border-radius: 0;
    }

    #FurnitureShowcase-{{ section.id }} .video-media video,
    #FurnitureShowcase-{{ section.id }} .video-media iframe {
      border-radius: 0;
    }
  }

  /* Ensure placeholder divs fill the container */
  #FurnitureShowcase-{{ section.id }} .furniture-item > div {
    width: 100%;
    height: 100%;
  }

  #FurnitureShowcase-{{ section.id }} .carousel-nav {
    position: absolute;
    top: 50%;
    left: -28px;
    right: -28px;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    pointer-events: none; /* Allow clicks to pass through to content */
    z-index: 10;
  }

  #FurnitureShowcase-{{ section.id }} .carousel-btn {
    background: none;
    border: none;
    width: 60px;
    height: 60px;
    cursor: pointer;
    pointer-events: auto; /* Re-enable clicks for buttons */
    position: relative;
    padding: 0;
  }

  #FurnitureShowcase-{{ section.id }} .carousel-btn img {
    width: 60px;
    height: 60px;
    transition: opacity 0.3s ease;
  }

  #FurnitureShowcase-{{ section.id }} .carousel-btn .default-icon {
    display: block;
  }

  #FurnitureShowcase-{{ section.id }} .carousel-btn .hover-icon {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
  }

  #FurnitureShowcase-{{ section.id }} .carousel-btn:hover .default-icon {
    display: none;
  }

  #FurnitureShowcase-{{ section.id }} .carousel-btn:hover .hover-icon {
    display: block;
  }

  /* Left arrow - hidden by default, visible on container hover */
  #FurnitureShowcase-{{ section.id }} .carousel-btn.prev-btn {
    opacity: 0;
    transition: opacity 0.3s ease, border-color 0.3s ease, color 0.3s ease, background 0.3s ease;
  }

  /* Right arrow - always visible */
  #FurnitureShowcase-{{ section.id }} .carousel-btn.next-btn {
    visibility: visible;
  }

  /* Show left arrow on carousel container hover */
  #FurnitureShowcase-{{ section.id }} .carousel-container:hover .carousel-btn.prev-btn {
    opacity: 1;
  }

  #FurnitureShowcase-{{ section.id }} .carousel-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }



  #FurnitureShowcase-{{ section.id }} .cta-section {
    margin-top: 30px;
    text-align: center;
  }
  
  #FurnitureShowcase-{{ section.id }} .btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid transparent;
  }
  
  #FurnitureShowcase-{{ section.id }} .btn-primary {
    background-color: #333;
    color: white;
  }
  
  #FurnitureShowcase-{{ section.id }} .btn-primary:hover {
    background-color: #555;
  }
  
  #FurnitureShowcase-{{ section.id }} .btn-secondary {
    background-color: transparent;
    color: #333;
    border-color: #333;
  }
  
  #FurnitureShowcase-{{ section.id }} .btn-secondary:hover {
    background-color: #333;
    color: white;
  }
  
  /* Slide layout styles */
  #FurnitureShowcase-{{ section.id }} .carousel-slide.mobile {
    display: none;
  }

  /* Slide layout styles */
  #FurnitureShowcase-{{ section.id }} .slide-container {
    height: calc(612/1720*(100vw - 200px));
    width: 100%;
    display: flex;
  }

  #FurnitureShowcase-{{ section.id }} .slide-main-image {
    height: calc(612/1720*(100vw - 200px));
    width: calc(407/1720*(100vw - 200px));
    background-color: #d4c4a8;
  }

  #FurnitureShowcase-{{ section.id }} .slide-main-image img {
    width: 100%;
    height: 100%;
    object-fit: fill;
  }

  #FurnitureShowcase-{{ section.id }} .slide-placeholder {
    background-color: #e8d5b7;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8b6f47;
    font-size: 18px;
  }

  #FurnitureShowcase-{{ section.id }} .slide-right-section {
    width: calc(1278/1720*(100vw - 200px));
    height: calc(612/1720*(100vw - 200px));
    margin-left: calc(32 / 1720 * (100vw - 200px));
  }

  #FurnitureShowcase-{{ section.id }} .slide-top-row {
    width: 100%;
    height: calc(290/1720*(100vw - 200px));
    display: flex;
  }

  #FurnitureShowcase-{{ section.id }} .slide-bottom-row {
    margin-top: calc(32 / 1720 * (100vw - 200px));
    width: 100%;
    height: calc(290/1720*(100vw - 200px));
    display: flex;
  }

  #FurnitureShowcase-{{ section.id }} .slide-image-left {
    width: calc(476/1720*(100vw - 200px));
    height: 100%;
    margin-right: calc(32 / 1720 * (100vw - 200px));
  }

  #FurnitureShowcase-{{ section.id }} .slide-image-left img {
    width: 100%;
    height: 100%;
    object-fit: fill;
  }

  #FurnitureShowcase-{{ section.id }} .slide-image-right {
    width: calc(770/1720*(100vw - 200px));
    height: 100%;
  }

  #FurnitureShowcase-{{ section.id }} .slide-image-right img {
    width: 100%;
    height: 100%;
    object-fit: fill;
  }

  #FurnitureShowcase-{{ section.id }} .slide-image-bottom-left {
    width: calc(770/1720*(100vw - 200px));
    height: 100%;
    margin-right: calc(32 / 1720 * (100vw - 200px));
  }

  #FurnitureShowcase-{{ section.id }} .slide-image-bottom-left img {
    width: 100%;
    height: 100%;
    object-fit: fill;
  }

  #FurnitureShowcase-{{ section.id }} .slide-image-bottom-right {
    width: calc(476/1720*(100vw - 200px));
    height: 100%;
  }

  #FurnitureShowcase-{{ section.id }} .slide-image-bottom-right img {
    width: 100%;
    height: 100%;
    object-fit: fill;
  }

  #FurnitureShowcase-{{ section.id }} .slide-small-placeholder {
    background-color: #d4c4a8;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8b6f47;
    font-size: 16px;
  }

  #FurnitureShowcase-{{ section.id }} .slide-medium-placeholder {
    background-color: #c9b896;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8b6f47;
    font-size: 16px;
  }

  @media only screen and (max-width: 768px) {
    #FurnitureShowcase-{{ section.id }} .carousel-slide.mobile {
      display: block;
      min-width: calc(337/353*(100vw - 32px));
      margin-right: 10px;
      flex-shrink: 0;
      scroll-snap-align: start;
    }

    #FurnitureShowcase-{{ section.id }} .carousel-wrapper {
      overflow-x: auto;
      overflow-y: hidden;
      scroll-behavior: smooth;
      scroll-snap-type: x mandatory;
      scrollbar-width: none;
      -ms-overflow-style: none;
      padding-right: 0;
    }

    #FurnitureShowcase-{{ section.id }} .carousel-wrapper::-webkit-scrollbar {
      display: none;
    }

    #FurnitureShowcase-{{ section.id }} .carousel-track {
      transition: none;
    }

    #FurnitureShowcase-{{ section.id }} .carousel-nav {
      display: none;
    }

    /* Mobile slide layout styles */
    #FurnitureShowcase-{{ section.id }} .mobile-slide-content {
      width: 100%;
      height: auto;
      padding: 0 16px;
    }

    #FurnitureShowcase-{{ section.id }} .mobile-slide-top {
      display: flex;
      width: 100%;
      margin-bottom: 11px;
    }

    #FurnitureShowcase-{{ section.id }} .mobile-slide-main-image {
      width: calc(163/353*100%);
      height: calc(245/353*(100vw - 64px));
      margin-right: calc(11/353*100%);
    }

    #FurnitureShowcase-{{ section.id }} .mobile-slide-main-image img {
      width: 100%;
      height: 100%;
      object-fit: fill;
    }

    #FurnitureShowcase-{{ section.id }} .mobile-slide-right {
      width: calc(163/353*100%);
      height: calc(245/353*(100vw - 64px));
      display: flex;
      flex-direction: column;
    }

    #FurnitureShowcase-{{ section.id }} .mobile-slide-small-image {
      width: 100%;
      height: calc(117/353*(100vw - 64px));
    }

    #FurnitureShowcase-{{ section.id }} .mobile-slide-small-image:first-child {
      margin-bottom: calc(11/353*(100vw - 64px));
    }

    #FurnitureShowcase-{{ section.id }} .mobile-slide-small-image img {
      width: 100%;
      height: 100%;
      object-fit: fill;
    }

    #FurnitureShowcase-{{ section.id }} .mobile-slide-bottom {
      width: 100%;
    }

    #FurnitureShowcase-{{ section.id }} .mobile-slide-bottom-image {
      width: 100%;
      height: calc(120/353*(100vw - 64px));
    }

    #FurnitureShowcase-{{ section.id }} .mobile-slide-bottom-image img {
      width: 100%;
      height: 100%;
      object-fit: fill;
    }
    #FurnitureShowcase-{{ section.id }} .section-title {
      font-family: PingFang SC;
      font-weight: 400;
      font-style: Regular;
      font-size: 12px;
      leading-trim: NONE;
      line-height: 14px;
      letter-spacing: 0px;

      text-align: left;
      margin-bottom: 6px;
      color: #999999;
      weight: 400;
      margin-left: auto;
      margin-right: auto;
    }

    .page-width{
      padding: 0 16px;
    }

    #FurnitureShowcase-{{ section.id }} .furniture-nav {
      gap: 15px;
      margin-bottom: 12px;
      flex-wrap: wrap;
    }

    #FurnitureShowcase-{{ section.id }} .furniture-nav button {
      padding: 0px 0;

      font-family: Playfair Display;
      font-weight: 400;
      font-style: Regular;
      font-size: 20px;
      leading-trim: NONE;
      line-height: 100%;
      letter-spacing: 0px;

    }

    #FurnitureShowcase-{{ section.id }} .furniture-grid {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 15px;
      height: auto;
    }

    /* Mobile responsive - reset grid positioning */
    #FurnitureShowcase-{{ section.id }} .furniture-item.image-1,
    #FurnitureShowcase-{{ section.id }} .furniture-item.image-2,
    #FurnitureShowcase-{{ section.id }} .furniture-item.image-3,
    #FurnitureShowcase-{{ section.id }} .furniture-item.image-4,
    #FurnitureShowcase-{{ section.id }} .furniture-item.image-5 {
      grid-column: unset;
      grid-row: unset;
      width: 100%;
      max-width: 350px;
      height: 180px;
    }

    #FurnitureShowcase-{{ section.id }} .carousel-slide.pc {
      display: none;
    }

    #FurnitureShowcase-{{ section.id }} .carousel-btn {
      width: 50px;
      height: 50px;
    }

    #FurnitureShowcase-{{ section.id }} .carousel-btn img {
      width: 50px;
      height: 50px;
    }

    #FurnitureShowcase-{{ section.id }} {
      {% if top_padding %}padding-top: 40px;{% endif %}
      {% if bottom_padding %}padding-bottom: 40px;{% endif %}
    }


  }
{% endstyle %}

<div id="FurnitureShowcase-{{ section.id }}" class="furniture-showcase-section">
  <div class="page-width">

    {%- if title != blank -%}
      <div class="section-title">
        {{ title }}
      </div>
    {%- endif -%}

    <!-- Navigation Menu -->
    <nav class="furniture-nav">
      <button class="nav-tab active" data-slide="0">
        {{ section.settings.nav_item_1 | default: 'Category 1' }}
      </button>
      <button class="nav-tab" data-slide="1">
        {{ section.settings.nav_item_2 | default: 'Category 2' }}
      </button>
      <button class="nav-tab" data-slide="2">
        {{ section.settings.nav_item_3 | default: 'Category 3' }}
      </button>
      <button class="nav-tab" data-slide="3">
        {{ section.settings.nav_item_4 | default: 'Category 4' }}
      </button>
      <button class="nav-tab" data-slide="4">
        {{ section.settings.nav_item_5 | default: 'Category 5' }}
      </button>
      <button class="nav-tab" data-slide="5">
        {{ section.settings.nav_item_6 | default: 'Category 6' }}
      </button>
    </nav>

    <!-- Carousel Container -->
    <div class="carousel-container">
      <div class="carousel-wrapper">
        <div class="carousel-track" id="carousel-track-{{ section.id }}">

          <!-- Slide 1 -->
          <div class="carousel-slide pc">
            <div class="slide-container">
              <div class="slide-main-image">
                {%- if section.settings.video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.image_1 != blank -%}
                  <img src="{{ section.settings.image_1 | image_url: width: 250 }}" alt="Slide 1 - Image 1" loading="lazy" width="250" height="612">
                {%- else -%}
                  <div class="slide-placeholder">
                    Slide 1 - Image 1
                  </div>
                {%- endif -%}
              </div>
              <div class="slide-right-section">
                <div class="slide-top-row">
                  <div class="slide-image-left">
                    {%- if section.settings.video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.image_2 != blank -%}
                      <img src="{{ section.settings.image_2 | image_url: width: 550 }}" alt="Slide 1 - Image 2" loading="lazy" width="476" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 1 - Image 2
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="slide-image-right">
                    {%- if section.settings.video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.image_3 != blank -%}
                      <img src="{{ section.settings.image_3 | image_url: width: 550 }}" alt="Slide 1 - Image 3" loading="lazy" width="770" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 1 - Image 3
                      </div>
                    {%- endif -%}
                  </div>
                </div>
                <div class="slide-bottom-row">
                    <div class="slide-image-bottom-left">
                    {%- if section.settings.video_url_4 != blank -%}
                      {%- render 'video-media', external_video: section.settings.video_url_4, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.video_4 != blank -%}
                      {%- render 'video-media', video: section.settings.video_4, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.image_4 != blank -%}
                      <img src="{{ section.settings.image_4 | image_url: width: 550 }}" alt="Slide 1 - Image 4" loading="lazy" width="770" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 1 - Image 4
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="slide-image-bottom-right">
                    {%- if section.settings.video_url_5 != blank -%}
                      {%- render 'video-media', external_video: section.settings.video_url_5, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.video_5 != blank -%}
                      {%- render 'video-media', video: section.settings.video_5, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.image_5 != blank -%}
                      <img src="{{ section.settings.image_5 | image_url: width: 550 }}" alt="Slide 1 - Image 5" loading="lazy" width="476" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 1 - Image 5
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Slide 2 -->
          <div class="carousel-slide pc">
            <div class="slide-container">
              <div class="slide-main-image">
                {%- if section.settings.slide2_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.slide2_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide2_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.slide2_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide2_image_1 != blank -%}
                  <img src="{{ section.settings.slide2_image_1 | image_url: width: 250 }}" alt="Slide 2 - Image 1" loading="lazy" width="250" height="612">
                {%- else -%}
                  <div class="slide-placeholder">
                    Slide 2 - Image 1
                  </div>
                {%- endif -%}
              </div>
              <div class="slide-right-section">
                <div class="slide-top-row">
                  <div class="slide-image-left">
                    {%- if section.settings.slide2_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide2_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.slide2_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_image_2 != blank -%}
                      <img src="{{ section.settings.slide2_image_2 | image_url: width: 550 }}" alt="Slide 2 - Image 2" loading="lazy" width="476" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 2 - Image 2
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="slide-image-right">
                    {%- if section.settings.slide2_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide2_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.slide2_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_image_3 != blank -%}
                      <img src="{{ section.settings.slide2_image_3 | image_url: width: 550 }}" alt="Slide 2 - Image 3" loading="lazy" width="770" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 2 - Image 3
                      </div>
                    {%- endif -%}
                  </div>
                </div>
                <div class="slide-bottom-row">
                    <div class="slide-image-bottom-left">
                    {%- if section.settings.slide2_video_url_4 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide2_video_url_4, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_video_4 != blank -%}
                      {%- render 'video-media', video: section.settings.slide2_video_4, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_image_4 != blank -%}
                      <img src="{{ section.settings.slide2_image_4 | image_url: width: 550 }}" alt="Slide 2 - Image 4" loading="lazy" width="770" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 2 - Image 4
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="slide-image-bottom-right">
                    {%- if section.settings.slide2_video_url_5 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide2_video_url_5, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_video_5 != blank -%}
                      {%- render 'video-media', video: section.settings.slide2_video_5, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_image_5 != blank -%}
                      <img src="{{ section.settings.slide2_image_5 | image_url: width: 550 }}" alt="Slide 2 - Image 5" loading="lazy" width="476" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 2 - Image 5
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Slide 3 -->
          <div class="carousel-slide pc">
            <div class="slide-container">
              <div class="slide-main-image">
                {%- if section.settings.slide3_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.slide3_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide3_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.slide3_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide3_image_1 != blank -%}
                  <img src="{{ section.settings.slide3_image_1 | image_url: width: 250 }}" alt="Slide 3 - Image 1" loading="lazy" width="250" height="612">
                {%- else -%}
                  <div class="slide-placeholder">
                    Slide 3 - Image 1
                  </div>
                {%- endif -%}
              </div>
              <div class="slide-right-section">
                <div class="slide-top-row">
                  <div class="slide-image-left">
                    {%- if section.settings.slide3_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide3_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.slide3_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_image_2 != blank -%}
                      <img src="{{ section.settings.slide3_image_2 | image_url: width: 550 }}" alt="Slide 3 - Image 2" loading="lazy" width="476" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 3 - Image 2
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="slide-image-right">
                    {%- if section.settings.slide3_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide3_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.slide3_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_image_3 != blank -%}
                      <img src="{{ section.settings.slide3_image_3 | image_url: width: 550 }}" alt="Slide 3 - Image 3" loading="lazy" width="770" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 3 - Image 3
                      </div>
                    {%- endif -%}
                  </div>
                </div>
                <div class="slide-bottom-row">
                    <div class="slide-image-bottom-left">
                    {%- if section.settings.slide3_video_url_4 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide3_video_url_4, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_video_4 != blank -%}
                      {%- render 'video-media', video: section.settings.slide3_video_4, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_image_4 != blank -%}
                      <img src="{{ section.settings.slide3_image_4 | image_url: width: 550 }}" alt="Slide 3 - Image 4" loading="lazy" width="770" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 3 - Image 4
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="slide-image-bottom-right">
                    {%- if section.settings.slide3_video_url_5 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide3_video_url_5, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_video_5 != blank -%}
                      {%- render 'video-media', video: section.settings.slide3_video_5, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_image_5 != blank -%}
                      <img src="{{ section.settings.slide3_image_5 | image_url: width: 550 }}" alt="Slide 3 - Image 5" loading="lazy" width="476" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 3 - Image 5
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Slide 4 -->
          <div class="carousel-slide pc">
            <div class="slide-container">
              <div class="slide-main-image">
                {%- if section.settings.slide4_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.slide4_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide4_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.slide4_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide4_image_1 != blank -%}
                  <img src="{{ section.settings.slide4_image_1 | image_url: width: 250 }}" alt="Slide 4 - Image 1" loading="lazy" width="250" height="612">
                {%- else -%}
                  <div class="slide-placeholder">
                    Slide 4 - Image 1
                  </div>
                {%- endif -%}
              </div>
              <div class="slide-right-section">
                <div class="slide-top-row">
                  <div class="slide-image-left">
                    {%- if section.settings.slide4_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide4_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.slide4_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_image_2 != blank -%}
                      <img src="{{ section.settings.slide4_image_2 | image_url: width: 550 }}" alt="Slide 4 - Image 2" loading="lazy" width="476" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 4 - Image 2
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="slide-image-right">
                    {%- if section.settings.slide4_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide4_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.slide4_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_image_3 != blank -%}
                      <img src="{{ section.settings.slide4_image_3 | image_url: width: 550 }}" alt="Slide 4 - Image 3" loading="lazy" width="770" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 4 - Image 3
                      </div>
                    {%- endif -%}
                  </div>
                </div>
                <div class="slide-bottom-row">
                    <div class="slide-image-bottom-left">
                    {%- if section.settings.slide4_video_url_4 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide4_video_url_4, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_video_4 != blank -%}
                      {%- render 'video-media', video: section.settings.slide4_video_4, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_image_4 != blank -%}
                      <img src="{{ section.settings.slide4_image_4 | image_url: width: 550 }}" alt="Slide 4 - Image 4" loading="lazy" width="770" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 4 - Image 4
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="slide-image-bottom-right">
                    {%- if section.settings.slide4_video_url_5 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide4_video_url_5, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_video_5 != blank -%}
                      {%- render 'video-media', video: section.settings.slide4_video_5, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_image_5 != blank -%}
                      <img src="{{ section.settings.slide4_image_5 | image_url: width: 550 }}" alt="Slide 4 - Image 5" loading="lazy" width="476" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 4 - Image 5
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Slide 5 -->
          <div class="carousel-slide pc">
            <div class="slide-container">
              <div class="slide-main-image">
                {%- if section.settings.slide5_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.slide5_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide5_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.slide5_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide5_image_1 != blank -%}
                  <img src="{{ section.settings.slide5_image_1 | image_url: width: 250 }}" alt="Slide 5 - Image 1" loading="lazy" width="250" height="612">
                {%- else -%}
                  <div class="slide-placeholder">
                    Slide 5 - Image 1
                  </div>
                {%- endif -%}
              </div>
              <div class="slide-right-section">
                <div class="slide-top-row">
                  <div class="slide-image-left">
                    {%- if section.settings.slide5_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide5_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.slide5_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_image_2 != blank -%}
                      <img src="{{ section.settings.slide5_image_2 | image_url: width: 550 }}" alt="Slide 5 - Image 2" loading="lazy" width="476" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 5 - Image 2
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="slide-image-right">
                    {%- if section.settings.slide5_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide5_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.slide5_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_image_3 != blank -%}
                      <img src="{{ section.settings.slide5_image_3 | image_url: width: 550 }}" alt="Slide 5 - Image 3" loading="lazy" width="770" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 5 - Image 3
                      </div>
                    {%- endif -%}
                  </div>
                </div>
                <div class="slide-bottom-row">
                    <div class="slide-image-bottom-left">
                    {%- if section.settings.slide5_video_url_4 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide5_video_url_4, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_video_4 != blank -%}
                      {%- render 'video-media', video: section.settings.slide5_video_4, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_image_4 != blank -%}
                      <img src="{{ section.settings.slide5_image_4 | image_url: width: 550 }}" alt="Slide 5 - Image 4" loading="lazy" width="770" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 5 - Image 4
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="slide-image-bottom-right">
                    {%- if section.settings.slide5_video_url_5 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide5_video_url_5, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_video_5 != blank -%}
                      {%- render 'video-media', video: section.settings.slide5_video_5, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_image_5 != blank -%}
                      <img src="{{ section.settings.slide5_image_5 | image_url: width: 550 }}" alt="Slide 5 - Image 5" loading="lazy" width="476" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 5 - Image 5
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Slide 6 -->
          <div class="carousel-slide pc">
            <div class="slide-container">
              <div class="slide-main-image">
                {%- if section.settings.slide6_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.slide6_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide6_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.slide6_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide6_image_1 != blank -%}
                  <img src="{{ section.settings.slide6_image_1 | image_url: width: 250 }}" alt="Slide 6 - Image 1" loading="lazy" width="250" height="612">
                {%- else -%}
                  <div class="slide-placeholder">
                    Slide 6 - Image 1
                  </div>
                {%- endif -%}
              </div>
              <div class="slide-right-section">
                <div class="slide-top-row">
                  <div class="slide-image-left">
                    {%- if section.settings.slide6_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide6_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.slide6_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_image_2 != blank -%}
                      <img src="{{ section.settings.slide6_image_2 | image_url: width: 550 }}" alt="Slide 6 - Image 2" loading="lazy" width="476" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 6 - Image 2
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="slide-image-right">
                    {%- if section.settings.slide6_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide6_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.slide6_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_image_3 != blank -%}
                      <img src="{{ section.settings.slide6_image_3 | image_url: width: 550 }}" alt="Slide 6 - Image 3" loading="lazy" width="770" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 6 - Image 3
                      </div>
                    {%- endif -%}
                  </div>
                </div>
                <div class="slide-bottom-row">
                    <div class="slide-image-bottom-left">
                    {%- if section.settings.slide6_video_url_4 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide6_video_url_4, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_video_4 != blank -%}
                      {%- render 'video-media', video: section.settings.slide6_video_4, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_image_4 != blank -%}
                      <img src="{{ section.settings.slide6_image_4 | image_url: width: 550 }}" alt="Slide 6 - Image 4" loading="lazy" width="770" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 6 - Image 4
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="slide-image-bottom-right">
                    {%- if section.settings.slide6_video_url_5 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide6_video_url_5, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_video_5 != blank -%}
                      {%- render 'video-media', video: section.settings.slide6_video_5, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_image_5 != blank -%}
                      <img src="{{ section.settings.slide6_image_5 | image_url: width: 550 }}" alt="Slide 6 - Image 5" loading="lazy" width="476" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 6 - Image 5
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Slide 1 Mobile -->
          <div class="carousel-slide mobile">
            <div class="" style="display: flex;width: calc(337/353*(100vw - 32px))">
              <div class="" style="width: calc(163/353*(100vw - 32px));height: calc(245/353*(100vw - 32px));">
                {%- if section.settings.mobile_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.mobile_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.mobile_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.mobile_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.mobile_image_1 != blank -%}
                  <img src="{{ section.settings.mobile_image_1 | image_url: width: 250 }}" alt="Slide 1 Mobile - Image 1" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="250" height="612">
                {%- elsif section.settings.video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.image_1 != blank -%}
                  <img src="{{ section.settings.image_1 | image_url: width: 250 }}" alt="Slide 1 - Image 1" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="250" height="612">
                {%- else -%}
                  <div class="slide-placeholder">
                    Slide 1 - Image 1
                  </div>
                {%- endif -%}
              </div>
              <div class="" style="width: calc(163/353*(100vw - 32px));margin-left: calc(11/353*(100vw - 32px));display: flex;height: calc(245/353*(100vw - 32px));">
                <div class="" style="width: 100%;height: 100%;">
                  <div class="" style="margin-bottom: calc(11/353*(100vw - 32px));width: 100%;height: calc(117/353*(100vw - 32px));">
                    {%- if section.settings.mobile_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.mobile_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.mobile_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.mobile_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.mobile_image_2 != blank -%}
                      <img src="{{ section.settings.mobile_image_2 | image_url: width: 550 }}" alt="Slide 1 Mobile - Image 2" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="476" height="290">
                    {%- elsif section.settings.video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.image_2 != blank -%}
                      <img src="{{ section.settings.image_2 | image_url: width: 550 }}" alt="Slide 1 - Image 2" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="476" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 1 - Image 2
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="" style="width: 100%;height: calc(117/353*(100vw - 32px));">
                    {%- if section.settings.mobile_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.mobile_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.mobile_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.mobile_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.mobile_image_3 != blank -%}
                      <img src="{{ section.settings.mobile_image_3 | image_url: width: 550 }}" alt="Slide 1 Mobile - Image 3" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
                    {%- elsif section.settings.video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.image_3 != blank -%}
                      <img src="{{ section.settings.image_3 | image_url: width: 550 }}" alt="Slide 1 - Image 3" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 1 - Image 3
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>
            <div class="" style="margin-top: calc(11/353*(100vw - 32px));width: calc(337/353*(100vw - 32px));height: calc(120/353*(100vw - 32px));">
              <div class="" style="height: 100%;width: 100%;">
              {%- if section.settings.mobile_video_url_4 != blank -%}
                {%- render 'video-media', external_video: section.settings.mobile_video_url_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.mobile_video_4 != blank -%}
                {%- render 'video-media', video: section.settings.mobile_video_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.mobile_image_4 != blank -%}
                <img src="{{ section.settings.mobile_image_4 | image_url: width: 550 }}" alt="Slide 1 Mobile - Image 4" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
              {%- elsif section.settings.video_url_4 != blank -%}
                {%- render 'video-media', external_video: section.settings.video_url_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.video_4 != blank -%}
                {%- render 'video-media', video: section.settings.video_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.image_4 != blank -%}
                <img src="{{ section.settings.image_4 | image_url: width: 550 }}" alt="Slide 1 - Image 4" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
              {%- else -%}
                <div class="slide-small-placeholder">
                  Slide 1 - Image 4
                </div>
              {%- endif -%}
              </div>
            </div>
          </div>

          <!-- Slide 2 Mobile -->
          <div class="carousel-slide mobile">
            <div class="" style="display: flex;width: 100%;">
              <div class="" style="width: calc(163/353*(100vw - 32px));height: calc(245/353*(100vw - 32px));">
                {%- if section.settings.slide2_mobile_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.slide2_mobile_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide2_mobile_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.slide2_mobile_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide2_mobile_image_1 != blank -%}
                  <img src="{{ section.settings.slide2_mobile_image_1 | image_url: width: 250 }}" alt="Slide 2 Mobile - Image 1" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="250" height="612">
                {%- elsif section.settings.slide2_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.slide2_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide2_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.slide2_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide2_image_1 != blank -%}
                  <img src="{{ section.settings.slide2_image_1 | image_url: width: 250 }}" alt="Slide 2 - Image 1" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="250" height="612">
                {%- else -%}
                  <div class="slide-placeholder">
                    Slide 2 - Image 1
                  </div>
                {%- endif -%}
              </div>
              <div class="" style="width: calc(163/353*(100vw - 32px));margin-left: calc(11/353*(100vw - 32px));display: flex;height: calc(245/353*(100vw - 32px));">
                <div class="" style="width: 100%;height: 100%;">
                  <div class="" style="margin-bottom: calc(11/353*(100vw - 32px));width: 100%;height: calc(117/353*(100vw - 32px));">
                    {%- if section.settings.slide2_mobile_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide2_mobile_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_mobile_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.slide2_mobile_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_mobile_image_2 != blank -%}
                      <img src="{{ section.settings.slide2_mobile_image_2 | image_url: width: 550 }}" alt="Slide 2 Mobile - Image 2" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="476" height="290">
                    {%- elsif section.settings.slide2_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide2_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.slide2_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_image_2 != blank -%}
                      <img src="{{ section.settings.slide2_image_2 | image_url: width: 550 }}" alt="Slide 2 - Image 2" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="476" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 2 - Image 2
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="" style="width: 100%;height: calc(117/353*(100vw - 32px));">
                    {%- if section.settings.slide2_mobile_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide2_mobile_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_mobile_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.slide2_mobile_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_mobile_image_3 != blank -%}
                      <img src="{{ section.settings.slide2_mobile_image_3 | image_url: width: 550 }}" alt="Slide 2 Mobile - Image 3" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
                    {%- elsif section.settings.slide2_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide2_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.slide2_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide2_image_3 != blank -%}
                      <img src="{{ section.settings.slide2_image_3 | image_url: width: 550 }}" alt="Slide 2 - Image 3" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 2 - Image 3
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>
            <div class="" style="margin-top: calc(11/353*(100vw - 32px));width: calc(337/353*(100vw - 32px));height: calc(120/353*(100vw - 32px));">
              <div class="" style="height: 100%;width: 100%;">
              {%- if section.settings.slide2_mobile_video_url_4 != blank -%}
                {%- render 'video-media', external_video: section.settings.slide2_mobile_video_url_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide2_mobile_video_4 != blank -%}
                {%- render 'video-media', video: section.settings.slide2_mobile_video_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide2_mobile_image_4 != blank -%}
                <img src="{{ section.settings.slide2_mobile_image_4 | image_url: width: 550 }}" alt="Slide 2 Mobile - Image 4" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
              {%- elsif section.settings.slide2_video_url_4 != blank -%}
                {%- render 'video-media', external_video: section.settings.slide2_video_url_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide2_video_4 != blank -%}
                {%- render 'video-media', video: section.settings.slide2_video_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide2_image_4 != blank -%}
                <img src="{{ section.settings.slide2_image_4 | image_url: width: 550 }}" alt="Slide 2 - Image 4" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
              {%- else -%}
                <div class="slide-small-placeholder">
                  Slide 2 - Image 4
                </div>
              {%- endif -%}
              </div>
            </div>
          </div>

          <!-- Slide 3 Mobile -->
          <div class="carousel-slide mobile">
            <div class="" style="display: flex;width: 100%;">
              <div class="" style="width: calc(163/353*(100vw - 32px));height: calc(245/353*(100vw - 32px));">
                {%- if section.settings.slide3_mobile_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.slide3_mobile_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide3_mobile_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.slide3_mobile_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide3_mobile_image_1 != blank -%}
                  <img src="{{ section.settings.slide3_mobile_image_1 | image_url: width: 250 }}" alt="Slide 3 Mobile - Image 1" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="250" height="612">
                {%- elsif section.settings.slide3_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.slide3_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide3_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.slide3_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide3_image_1 != blank -%}
                  <img src="{{ section.settings.slide3_image_1 | image_url: width: 250 }}" alt="Slide 3 - Image 1" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="250" height="612">
                {%- else -%}
                  <div class="slide-placeholder">
                    Slide 3 - Image 1
                  </div>
                {%- endif -%}
              </div>
              <div class="" style="width: calc(163/353*(100vw - 32px));margin-left: calc(11/353*(100vw - 32px));display: flex;height: calc(245/353*(100vw - 32px));">
                <div class="" style="width: 100%;height: 100%;">
                  <div class="" style="margin-bottom: calc(11/353*(100vw - 32px));width: 100%;height: calc(117/353*(100vw - 32px));">
                    {%- if section.settings.slide3_mobile_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide3_mobile_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_mobile_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.slide3_mobile_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_mobile_image_2 != blank -%}
                      <img src="{{ section.settings.slide3_mobile_image_2 | image_url: width: 550 }}" alt="Slide 3 Mobile - Image 2" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="476" height="290">
                    {%- elsif section.settings.slide3_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide3_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.slide3_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_image_2 != blank -%}
                      <img src="{{ section.settings.slide3_image_2 | image_url: width: 550 }}" alt="Slide 3 - Image 2" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="476" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 3 - Image 2
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="" style="width: 100%;height: calc(117/353*(100vw - 32px));">
                    {%- if section.settings.slide3_mobile_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide3_mobile_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_mobile_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.slide3_mobile_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_mobile_image_3 != blank -%}
                      <img src="{{ section.settings.slide3_mobile_image_3 | image_url: width: 550 }}" alt="Slide 3 Mobile - Image 3" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
                    {%- elsif section.settings.slide3_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide3_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.slide3_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide3_image_3 != blank -%}
                      <img src="{{ section.settings.slide3_image_3 | image_url: width: 550 }}" alt="Slide 3 - Image 3" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 3 - Image 3
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>
            <div class="" style="margin-top: calc(11/353*(100vw - 32px));width: calc(337/353*(100vw - 32px));height: calc(120/353*(100vw - 32px));">
              <div class="" style="height: 100%;width: 100%;">
              {%- if section.settings.slide3_mobile_video_url_4 != blank -%}
                {%- render 'video-media', external_video: section.settings.slide3_mobile_video_url_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide3_mobile_video_4 != blank -%}
                {%- render 'video-media', video: section.settings.slide3_mobile_video_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide3_mobile_image_4 != blank -%}
                <img src="{{ section.settings.slide3_mobile_image_4 | image_url: width: 550 }}" alt="Slide 3 Mobile - Image 4" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
              {%- elsif section.settings.slide3_video_url_4 != blank -%}
                {%- render 'video-media', external_video: section.settings.slide3_video_url_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide3_video_4 != blank -%}
                {%- render 'video-media', video: section.settings.slide3_video_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide3_image_4 != blank -%}
                <img src="{{ section.settings.slide3_image_4 | image_url: width: 550 }}" alt="Slide 3 - Image 4" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
              {%- else -%}
                <div class="slide-small-placeholder">
                  Slide 3 - Image 4
                </div>
              {%- endif -%}
              </div>
            </div>
          </div>

          <!-- Slide 4 Mobile -->
          <div class="carousel-slide mobile">
            <div class="" style="display: flex;width: 100%;">
              <div class="" style="width: calc(163/353*(100vw - 32px));height: calc(245/353*(100vw - 32px));">
                {%- if section.settings.slide4_mobile_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.slide4_mobile_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide4_mobile_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.slide4_mobile_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide4_mobile_image_1 != blank -%}
                  <img src="{{ section.settings.slide4_mobile_image_1 | image_url: width: 250 }}" alt="Slide 4 Mobile - Image 1" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="250" height="612">
                {%- elsif section.settings.slide4_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.slide4_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide4_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.slide4_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide4_image_1 != blank -%}
                  <img src="{{ section.settings.slide4_image_1 | image_url: width: 250 }}" alt="Slide 4 - Image 1" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="250" height="612">
                {%- else -%}
                  <div class="slide-placeholder">
                    Slide 4 - Image 1
                  </div>
                {%- endif -%}
              </div>
              <div class="" style="width: calc(163/353*(100vw - 32px));margin-left: calc(11/353*(100vw - 32px));display: flex;height: calc(245/353*(100vw - 32px));">
                <div class="" style="width: 100%;height: 100%;">
                  <div class="" style="margin-bottom: calc(11/353*(100vw - 32px));width: 100%;height: calc(117/353*(100vw - 32px));">
                    {%- if section.settings.slide4_mobile_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide4_mobile_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_mobile_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.slide4_mobile_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_mobile_image_2 != blank -%}
                      <img src="{{ section.settings.slide4_mobile_image_2 | image_url: width: 550 }}" alt="Slide 4 Mobile - Image 2" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="476" height="290">
                    {%- elsif section.settings.slide4_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide4_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.slide4_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_image_2 != blank -%}
                      <img src="{{ section.settings.slide4_image_2 | image_url: width: 550 }}" alt="Slide 4 - Image 2" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="476" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 4 - Image 2
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="" style="width: 100%;height: calc(117/353*(100vw - 32px));">
                    {%- if section.settings.slide4_mobile_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide4_mobile_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_mobile_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.slide4_mobile_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_mobile_image_3 != blank -%}
                      <img src="{{ section.settings.slide4_mobile_image_3 | image_url: width: 550 }}" alt="Slide 4 Mobile - Image 3" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
                    {%- elsif section.settings.slide4_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide4_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.slide4_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide4_image_3 != blank -%}
                      <img src="{{ section.settings.slide4_image_3 | image_url: width: 550 }}" alt="Slide 4 - Image 3" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 4 - Image 3
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>
            <div class="" style="margin-top: calc(11/353*(100vw - 32px));width: calc(337/353*(100vw - 32px));height: calc(120/353*(100vw - 32px));">
              <div class="" style="height: 100%;width: 100%;">
              {%- if section.settings.slide4_mobile_video_url_4 != blank -%}
                {%- render 'video-media', external_video: section.settings.slide4_mobile_video_url_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide4_mobile_video_4 != blank -%}
                {%- render 'video-media', video: section.settings.slide4_mobile_video_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide4_mobile_image_4 != blank -%}
                <img src="{{ section.settings.slide4_mobile_image_4 | image_url: width: 550 }}" alt="Slide 4 Mobile - Image 4" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
              {%- elsif section.settings.slide4_video_url_4 != blank -%}
                {%- render 'video-media', external_video: section.settings.slide4_video_url_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide4_video_4 != blank -%}
                {%- render 'video-media', video: section.settings.slide4_video_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide4_image_4 != blank -%}
                <img src="{{ section.settings.slide4_image_4 | image_url: width: 550 }}" alt="Slide 4 - Image 4" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
              {%- else -%}
                <div class="slide-small-placeholder">
                  Slide 4 - Image 4
                </div>
              {%- endif -%}
              </div>
            </div>
          </div>

          <!-- Slide 5 Mobile -->
          <div class="carousel-slide mobile">
            <div class="" style="display: flex;width: 100%;">
              <div class="" style="width: calc(163/353*(100vw - 32px));height: calc(245/353*(100vw - 32px));">
                {%- if section.settings.slide5_mobile_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.slide5_mobile_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide5_mobile_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.slide5_mobile_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide5_mobile_image_1 != blank -%}
                  <img src="{{ section.settings.slide5_mobile_image_1 | image_url: width: 250 }}" alt="Slide 5 Mobile - Image 1" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="250" height="612">
                {%- elsif section.settings.slide5_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.slide5_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide5_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.slide5_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide5_image_1 != blank -%}
                  <img src="{{ section.settings.slide5_image_1 | image_url: width: 250 }}" alt="Slide 5 - Image 1" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="250" height="612">
                {%- else -%}
                  <div class="slide-placeholder">
                    Slide 5 - Image 1
                  </div>
                {%- endif -%}
              </div>
              <div class="" style="width: calc(163/353*(100vw - 32px));margin-left: calc(11/353*(100vw - 32px));display: flex;height: calc(245/353*(100vw - 32px));">
                <div class="" style="width: 100%;height: 100%;">
                  <div class="" style="margin-bottom: calc(11/353*(100vw - 32px));width: 100%;height: calc(117/353*(100vw - 32px));">
                    {%- if section.settings.slide5_mobile_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide5_mobile_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_mobile_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.slide5_mobile_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_mobile_image_2 != blank -%}
                      <img src="{{ section.settings.slide5_mobile_image_2 | image_url: width: 550 }}" alt="Slide 5 Mobile - Image 2" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="476" height="290">
                    {%- elsif section.settings.slide5_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide5_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.slide5_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_image_2 != blank -%}
                      <img src="{{ section.settings.slide5_image_2 | image_url: width: 550 }}" alt="Slide 5 - Image 2" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="476" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 5 - Image 2
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="" style="width: 100%;height: calc(117/353*(100vw - 32px));">
                    {%- if section.settings.slide5_mobile_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide5_mobile_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_mobile_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.slide5_mobile_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_mobile_image_3 != blank -%}
                      <img src="{{ section.settings.slide5_mobile_image_3 | image_url: width: 550 }}" alt="Slide 5 Mobile - Image 3" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
                    {%- elsif section.settings.slide5_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide5_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.slide5_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide5_image_3 != blank -%}
                      <img src="{{ section.settings.slide5_image_3 | image_url: width: 550 }}" alt="Slide 5 - Image 3" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 5 - Image 3
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>
            <div class="" style="margin-top: calc(11/353*(100vw - 32px));width: calc(337/353*(100vw - 32px));height: calc(120/353*(100vw - 32px));">
              <div class="" style="height: 100%;width: 100%;">
              {%- if section.settings.slide5_mobile_video_url_4 != blank -%}
                {%- render 'video-media', external_video: section.settings.slide5_mobile_video_url_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide5_mobile_video_4 != blank -%}
                {%- render 'video-media', video: section.settings.slide5_mobile_video_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide5_mobile_image_4 != blank -%}
                <img src="{{ section.settings.slide5_mobile_image_4 | image_url: width: 550 }}" alt="Slide 5 Mobile - Image 4" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
              {%- elsif section.settings.slide5_video_url_4 != blank -%}
                {%- render 'video-media', external_video: section.settings.slide5_video_url_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide5_video_4 != blank -%}
                {%- render 'video-media', video: section.settings.slide5_video_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide5_image_4 != blank -%}
                <img src="{{ section.settings.slide5_image_4 | image_url: width: 550 }}" alt="Slide 5 - Image 4" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
              {%- else -%}
                <div class="slide-small-placeholder">
                  Slide 5 - Image 4
                </div>
              {%- endif -%}
              </div>
            </div>
          </div>

          <!-- Slide 6 Mobile -->
          <div class="carousel-slide mobile">
            <div class="" style="display: flex;width: 100%;">
              <div class="" style="width: calc(163/353*(100vw - 32px));height: calc(245/353*(100vw - 32px));">
                {%- if section.settings.slide6_mobile_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.slide6_mobile_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide6_mobile_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.slide6_mobile_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide6_mobile_image_1 != blank -%}
                  <img src="{{ section.settings.slide6_mobile_image_1 | image_url: width: 250 }}" alt="Slide 6 Mobile - Image 1" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="250" height="612">
                {%- elsif section.settings.slide6_video_url_1 != blank -%}
                  {%- render 'video-media', external_video: section.settings.slide6_video_url_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide6_video_1 != blank -%}
                  {%- render 'video-media', video: section.settings.slide6_video_1, autoplay: true, loop: true, muted: true -%}
                {%- elsif section.settings.slide6_image_1 != blank -%}
                  <img src="{{ section.settings.slide6_image_1 | image_url: width: 250 }}" alt="Slide 6 - Image 1" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="250" height="612">
                {%- else -%}
                  <div class="slide-placeholder">
                    Slide 6 - Image 1
                  </div>
                {%- endif -%}
              </div>
              <div class="" style="width: calc(163/353*(100vw - 32px));margin-left: calc(11/353*(100vw - 32px));display: flex;height: calc(245/353*(100vw - 32px));">
                <div class="" style="width: 100%;height: 100%;">
                  <div class="" style="margin-bottom: calc(11/353*(100vw - 32px));width: 100%;height: calc(117/353*(100vw - 32px));">
                    {%- if section.settings.slide6_mobile_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide6_mobile_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_mobile_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.slide6_mobile_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_mobile_image_2 != blank -%}
                      <img src="{{ section.settings.slide6_mobile_image_2 | image_url: width: 550 }}" alt="Slide 6 Mobile - Image 2" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="476" height="290">
                    {%- elsif section.settings.slide6_video_url_2 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide6_video_url_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_video_2 != blank -%}
                      {%- render 'video-media', video: section.settings.slide6_video_2, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_image_2 != blank -%}
                      <img src="{{ section.settings.slide6_image_2 | image_url: width: 550 }}" alt="Slide 6 - Image 2" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="476" height="290">
                    {%- else -%}
                      <div class="slide-small-placeholder">
                        Slide 6 - Image 2
                      </div>
                    {%- endif -%}
                  </div>
                  <div class="" style="width: 100%;height: calc(117/353*(100vw - 32px));">
                    {%- if section.settings.slide6_mobile_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide6_mobile_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_mobile_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.slide6_mobile_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_mobile_image_3 != blank -%}
                      <img src="{{ section.settings.slide6_mobile_image_3 | image_url: width: 550 }}" alt="Slide 6 Mobile - Image 3" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
                    {%- elsif section.settings.slide6_video_url_3 != blank -%}
                      {%- render 'video-media', external_video: section.settings.slide6_video_url_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_video_3 != blank -%}
                      {%- render 'video-media', video: section.settings.slide6_video_3, autoplay: true, loop: true, muted: true -%}
                    {%- elsif section.settings.slide6_image_3 != blank -%}
                      <img src="{{ section.settings.slide6_image_3 | image_url: width: 550 }}" alt="Slide 6 - Image 3" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
                    {%- else -%}
                      <div class="slide-medium-placeholder">
                        Slide 6 - Image 3
                      </div>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>
            <div class="" style="margin-top: calc(11/353*(100vw - 32px));width: calc(337/353*(100vw - 32px));height: calc(120/353*(100vw - 32px));">
              <div class="" style="height: 100%;width: 100%;">
              {%- if section.settings.slide6_mobile_video_url_4 != blank -%}
                {%- render 'video-media', external_video: section.settings.slide6_mobile_video_url_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide6_mobile_video_4 != blank -%}
                {%- render 'video-media', video: section.settings.slide6_mobile_video_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide6_mobile_image_4 != blank -%}
                <img src="{{ section.settings.slide6_mobile_image_4 | image_url: width: 550 }}" alt="Slide 6 Mobile - Image 4" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
              {%- elsif section.settings.slide6_video_url_4 != blank -%}
                {%- render 'video-media', external_video: section.settings.slide6_video_url_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide6_video_4 != blank -%}
                {%- render 'video-media', video: section.settings.slide6_video_4, autoplay: true, loop: true, muted: true -%}
              {%- elsif section.settings.slide6_image_4 != blank -%}
                <img src="{{ section.settings.slide6_image_4 | image_url: width: 550 }}" alt="Slide 6 - Image 4" loading="lazy" style="object-fit: fill;height: 100%;width: 100%;" width="770" height="290">
              {%- else -%}
                <div class="slide-small-placeholder">
                  Slide 6 - Image 4
                </div>
              {%- endif -%}
              </div>
            </div>
          </div>

        </div>
      </div>

      <!-- Carousel Navigation -->
      <div class="carousel-nav">
        <button class="carousel-btn prev-btn" id="prev-btn-{{ section.id }}">
          <img src="{{ 'Icon_Collection_Left Arrow 3_60px_Default.svg' | asset_url }}" alt="Previous" class="default-icon" width="60" height="60">
          <img src="{{ 'Icon_Collection_Left Arrow 3_60px_Default-1.svg' | asset_url }}" alt="Previous" class="hover-icon" width="60" height="60">
        </button>

        <button class="carousel-btn next-btn" id="next-btn-{{ section.id }}">
          <img src="{{ 'Icon_Collection_Right Arrow 3_60px_Default.svg' | asset_url }}" alt="Next" class="default-icon" width="60" height="60">
          <img src="{{ 'Icon_Collection_Right Arrow 3_60px_Default-1.svg' | asset_url }}" alt="Next" class="hover-icon" width="60" height="60">
        </button>
      </div>


    </div>

    <!-- Call to Action -->
    {%- if button_label != blank -%}
      <div class="cta-section">
        <a href="{{ nav_links_array[0] | default: button_link | default: '#' }}"
           class="btn btn-{{ button_style }}"
           id="cta-button-{{ section.id }}"
           data-nav-links="{{ nav_links }}"
           data-default-link="{{ button_link | default: '#' }}">
          {{ button_label }}
        </a>
      </div>
    {%- endif -%}

  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const sectionId = '{{ section.id }}';
  const carousel = document.getElementById('carousel-track-' + sectionId);
  const navTabs = document.querySelectorAll('#FurnitureShowcase-' + sectionId + ' .nav-tab');
  const prevBtn = document.getElementById('prev-btn-' + sectionId);
  const nextBtn = document.getElementById('next-btn-' + sectionId);
  const carouselContainer = document.querySelector('#FurnitureShowcase-' + sectionId);
  const carouselInnerContainer = document.querySelector('#FurnitureShowcase-' + sectionId + ' .carousel-container');
  const ctaButton = document.getElementById('cta-button-' + sectionId);


  if (!carousel) {
    console.error('Carousel track not found for section:', sectionId);
    return;
  }

  let currentSlide = 0;

  // Get slides based on device type
  function getActiveSlides() {
    const isMobile = window.innerWidth <= 768;
    if (isMobile) {
      return document.querySelectorAll('#FurnitureShowcase-' + sectionId + ' .carousel-slide.mobile');
    } else {
      return document.querySelectorAll('#FurnitureShowcase-' + sectionId + ' .carousel-slide.pc');
    }
  }

  let slides = getActiveSlides();
  let totalSlides = slides.length;

  // Auto-play settings
  let autoPlayInterval = null;
  let isAutoPlaying = {{ auto_play | json }};
  const autoPlayDelay = {{ auto_play_speed | times: 1000 }}; // Convert seconds to milliseconds
  let isPaused = false;
  let resumeTimeout = null; // Track resume timeout to prevent multiple timers

  const isMobile = window.innerWidth <= 768;

  // Only proceed if we have slides
  if (totalSlides === 0) {
    return;
  }

  function updateCarousel() {
    if (!carousel) return;

    // Check if we're on mobile (768px or less)
    const isMobile = window.innerWidth <= 768;

    // Update slides and totalSlides based on current device type
    slides = getActiveSlides();
    totalSlides = slides.length;

    // Ensure currentSlide is within bounds
    if (currentSlide >= totalSlides) {
      currentSlide = 0;
    }

    // Calculate translateX based on device type
    let translateX;
    if (isMobile) {
      // On mobile, each slide is calc(100vw - 32px - 6px) with 6px margin-right
      // Total width per slide including margin = (100vw - 32px - 6px) + 6px = (100vw - 32px)
      // Since the carousel wrapper has page-width padding of 16px on each side (32px total),
      // each slide takes up 100% of the available width
      translateX = -currentSlide * 100;
    } else {
      // On desktop, use the original 100% calculation
      translateX = -currentSlide * 100;
    }

    carousel.style.transform = `translateX(${translateX}%)`;

    // Update nav tabs
    navTabs.forEach((tab, index) => {
      if (index < totalSlides) {
        tab.classList.toggle('active', index === currentSlide);
      }
    });

    // Update button states - remove disabled state for infinite loop
    if (prevBtn) {
      prevBtn.disabled = false;
    }
    if (nextBtn) {
      nextBtn.disabled = false;
    }

    // Update CTA button link based on current slide
    if (ctaButton) {
      const navLinksData = ctaButton.getAttribute('data-nav-links');
      const defaultLink = ctaButton.getAttribute('data-default-link');

      if (navLinksData) {
        const navLinksArray = navLinksData.split('|');
        const currentLink = navLinksArray[currentSlide];

        // Use the current slide's link if it exists and is not empty, otherwise use default
        if (currentLink && currentLink.trim() !== '') {
          ctaButton.href = currentLink;
        } else {
          ctaButton.href = defaultLink || '#';
        }
      }
    }

  }

  function goToSlide(slideIndex) {
    // Update totalSlides to current device type
    slides = getActiveSlides();
    totalSlides = slides.length;

    if (slideIndex >= 0 && slideIndex < totalSlides) {
      currentSlide = slideIndex;
      updateCarousel();
    }
  }

  function nextSlide() {
    // Update totalSlides to current device type
    slides = getActiveSlides();
    totalSlides = slides.length;

    // Infinite loop: go to first slide after last slide
    currentSlide = (currentSlide + 1) % totalSlides;
    updateCarousel();
  }

  function prevSlide() {
    // Update totalSlides to current device type
    slides = getActiveSlides();
    totalSlides = slides.length;

    // Infinite loop: go to last slide before first slide
    currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
    updateCarousel();
  }

  // Auto-play functions

  function startAutoPlay() {
    if (!isAutoPlaying || isPaused) return;

    autoPlayInterval = setInterval(() => {
      if (!isPaused) {
        nextSlide();
      }
    }, autoPlayDelay);
  }

  function stopAutoPlay() {
    if (autoPlayInterval) {
      clearInterval(autoPlayInterval);
      autoPlayInterval = null;
    }
  }

  function pauseAutoPlay() {
    isPaused = true;
    stopAutoPlay();
    // Clear any pending resume timeout
    if (resumeTimeout) {
      clearTimeout(resumeTimeout);
      resumeTimeout = null;
    }
  }

  function resumeAutoPlay() {
    isPaused = false;
    if (isAutoPlaying) {
      startAutoPlay();
    }
  }

  function scheduleResumeAutoPlay() {
    // Clear any existing resume timeout first
    if (resumeTimeout) {
      clearTimeout(resumeTimeout);
    }
    // Schedule resume after 3 seconds
    resumeTimeout = setTimeout(() => {
      resumeAutoPlay();
      resumeTimeout = null;
    }, 3000);
  }

  // Event listeners for navigation tabs
  navTabs.forEach((tab, index) => {
    tab.addEventListener('click', (e) => {
      e.preventDefault();
      pauseAutoPlay(); // Pause auto-play when user interacts
      goToSlide(index);
      // Resume auto-play after a delay
      scheduleResumeAutoPlay();
    });
  });

  // Event listeners for prev/next buttons
  if (prevBtn) {
    prevBtn.addEventListener('click', () => {
      pauseAutoPlay();
      prevSlide();
      scheduleResumeAutoPlay();
    });
  }
  if (nextBtn) {
    nextBtn.addEventListener('click', () => {
      pauseAutoPlay();
      nextSlide();
      scheduleResumeAutoPlay();
    });
  }

  // Keyboard navigation (only when focused on the carousel area)
  if (carouselContainer) {
    carouselContainer.addEventListener('keydown', function(e) {
      if (e.key === 'ArrowLeft') {
        e.preventDefault();
        pauseAutoPlay();
        prevSlide();
        scheduleResumeAutoPlay();
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        pauseAutoPlay();
        nextSlide();
        scheduleResumeAutoPlay();
      }
    });

    // Pause on hover/mouse enter - only on carousel-container
    if (carouselInnerContainer) {
      carouselInnerContainer.addEventListener('mouseenter', pauseAutoPlay);
      carouselInnerContainer.addEventListener('mouseleave', resumeAutoPlay);
    }

    // Touch events for mobile
    let touchStartX = 0;
    let touchEndX = 0;
    let touchStartTime = 0;
    let isScrolling = false;

    // For mobile, use native scroll behavior with snap points
    const isMobileDevice = window.innerWidth <= 768;
    const carouselWrapper = document.querySelector('#FurnitureShowcase-' + sectionId + ' .carousel-wrapper');

    if (isMobileDevice && carouselWrapper) {
      // Enable native scrolling for mobile
      carouselWrapper.addEventListener('scroll', () => {
        // Pause auto-play during scroll
        pauseAutoPlay();

        // Clear any existing resume timeout
        if (resumeTimeout) {
          clearTimeout(resumeTimeout);
        }

        // Schedule resume after scroll stops
        resumeTimeout = setTimeout(() => {
          // Update current slide based on scroll position
          const scrollLeft = carouselWrapper.scrollLeft;
          const slideWidth = carouselWrapper.clientWidth;
          const newSlide = Math.round(scrollLeft / slideWidth);

          if (newSlide !== currentSlide && newSlide >= 0 && newSlide < totalSlides) {
            currentSlide = newSlide;
            // Update nav tabs
            navTabs.forEach((tab, index) => {
              if (index < totalSlides) {
                tab.classList.toggle('active', index === currentSlide);
              }
            });
          }

          resumeAutoPlay();
          resumeTimeout = null;
        }, 150);
      }, { passive: true });

      // Override updateCarousel for mobile to use scroll position
      const originalUpdateCarousel = updateCarousel;
      updateCarousel = function() {
        if (window.innerWidth <= 768 && carouselWrapper) {
          // For mobile, scroll to the correct position
          const slideWidth = carouselWrapper.clientWidth;
          const scrollLeft = currentSlide * slideWidth;
          carouselWrapper.scrollTo({
            left: scrollLeft,
            behavior: 'smooth'
          });

          // Update nav tabs
          navTabs.forEach((tab, index) => {
            if (index < totalSlides) {
              tab.classList.toggle('active', index === currentSlide);
            }
          });
        } else {
          // Use original function for desktop
          originalUpdateCarousel();
        }
      };
    } else {
      // Fallback touch events for older mobile browsers
      carouselContainer.addEventListener('touchstart', (e) => {
        touchStartX = e.changedTouches[0].screenX;
        touchStartTime = Date.now();
        isScrolling = false;
        pauseAutoPlay();
      }, { passive: true });

      carouselContainer.addEventListener('touchmove', (e) => {
        isScrolling = true;
      }, { passive: true });

      carouselContainer.addEventListener('touchend', (e) => {
        touchEndX = e.changedTouches[0].screenX;
        const touchDuration = Date.now() - touchStartTime;

        if (!isScrolling && touchDuration < 300) {
          handleSwipe();
        }
        scheduleResumeAutoPlay();
      }, { passive: true });

      function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
          if (diff > 0) {
            // Swipe left - next slide
            nextSlide();
          } else {
            // Swipe right - previous slide
            prevSlide();
          }
        }
      }
    }

    // Pause when page becomes hidden (tab switching, etc.)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        pauseAutoPlay();
      } else {
        resumeAutoPlay();
      }
    });
  }

  // Initialize
  updateCarousel();

  // Add window resize listener to recalculate carousel position
  window.addEventListener('resize', function() {
    updateCarousel();
  });

  // Start auto-play only if enabled
  if (isAutoPlaying) {
    startAutoPlay();
  } else {
  }
});
</script>

{%- if divider -%}
  <hr class="section-divider">
{%- endif -%}
