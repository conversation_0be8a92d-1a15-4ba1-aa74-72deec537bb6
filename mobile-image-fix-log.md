# 移动端图片保存问题修复日志

## 问题描述
用户反馈移动端上传的图片没有保存成功，实际上是移动端图片显示逻辑存在问题。

## 问题根因分析

### 原始逻辑问题
1. **全局检查逻辑**：代码会检查是否有任何幻灯片设置了移动端图片，设置`has_mobile_images`变量
2. **条件显示逻辑**：当`has_mobile_images`为true时，只有设置了移动端图片的幻灯片才会在移动端显示图片
3. **隐藏逻辑**：没有设置移动端图片的幻灯片在移动端会被完全隐藏或不显示图片

### 问题表现
- 用户上传了移动端图片，但某些幻灯片没有设置移动端图片
- 这些幻灯片在移动端不显示任何图片，给用户造成"图片没有保存成功"的错觉

## 修复方案

### 新的显示逻辑
```liquid
<!-- 移动端图片逻辑 -->
{% if block.settings.mobile_image != blank %}
    <img class="mobile-image" src="{{ block.settings.mobile_image | img_url: '800x600' }}" alt="{{ block.settings.title }}">
{% else %}
    <img class="mobile-image" src="{{ block.settings.image | img_url: '800x600' }}" alt="{{ block.settings.title }}">
{% endif %}
```

### 修复内容

#### 1. 简化移动端图片显示逻辑
- **修改前**：复杂的全局检查和条件显示
- **修改后**：每个幻灯片独立判断，有移动端图片就用移动端图片，没有就用Web端图片作为后备

#### 2. 移除不必要的JavaScript逻辑
- **修改前**：JavaScript需要动态添加`no-mobile-image`类来隐藏幻灯片
- **修改后**：每个幻灯片都会显示图片，不需要隐藏逻辑

#### 3. 清理CSS样式
- **修改前**：复杂的`.has-mobile-images`和`.no-mobile-image`类样式
- **修改后**：简化的移动端图片显示样式

#### 4. 简化HTML结构
- **修改前**：动态添加`has-mobile-images`和`no-mobile-image`类
- **修改后**：简洁的HTML结构，不需要额外的类

## 修复效果

### 用户体验改善
1. **一致性**：每个幻灯片在移动端都会显示图片
2. **后备机制**：没有设置移动端图片的幻灯片会自动使用Web端图片
3. **直观性**：用户上传的移动端图片会正确显示，没有上传的会有合理的后备显示

### 技术改进
1. **代码简化**：移除了复杂的条件判断逻辑
2. **维护性**：更容易理解和维护的代码结构
3. **性能**：减少了不必要的JavaScript处理

## 测试建议

### 测试场景
1. **全部设置移动端图片**：所有幻灯片都设置了移动端图片
2. **部分设置移动端图片**：部分幻灯片设置了移动端图片，部分没有
3. **全部未设置移动端图片**：所有幻灯片都只有Web端图片

### 预期结果
- 所有场景下，移动端都应该正确显示图片
- 设置了移动端图片的显示移动端图片
- 没有设置移动端图片的显示Web端图片作为后备

## 修改文件
- `sections/case-banner.liquid`：主要修改文件

## 修改时间
- 2025-07-27
