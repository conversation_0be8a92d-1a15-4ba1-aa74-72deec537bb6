/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "current": {
    "color_body_bg": "#ffffff",
    "color_body_text": "#000000",
    "color_price": "#ba4444",
    "color_savings_text": "#1c1d1d",
    "color_borders": "#e8e8e1",
    "color_button": "#111111",
    "color_button_text": "#ffffff",
    "color_sale_tag": "#ba4444",
    "color_cart_dot": "#d75d43",
    "color_cart_dot_text": "#ffffff",
    "color_small_image_bg": "#ffffff",
    "color_large_image_bg": "#0f0f0f",
    "color_header": "#ffffff",
    "color_header_text": "#111111",
    "color_announcement": "#6f5034",
    "color_announcement_text": "#ffffff",
    "color_header_search": "#ffffff",
    "color_footer": "#ffffff",
    "color_footer_border": "#e8e8e1",
    "color_footer_text": "#000000",
    "color_scheme_1_bg": "#d75d43",
    "color_scheme_1_text": "#ffffff",
    "color_scheme_1_texture": "wave",
    "color_scheme_2_bg": "#f4e3da",
    "color_scheme_2_text": "#000000",
    "color_scheme_2_texture": "wave",
    "color_scheme_3_bg": "#fcf4f1",
    "color_scheme_3_text": "#000000",
    "color_scheme_3_texture": "wave",
    "type_header_font_family": "sans-serif",
    "type_header_spacing": "25",
    "type_header_base_size": 35,
    "type_header_line_height": 1.1,
    "type_header_capitalize": false,
    "type_base_font_family": "sans-serif",
    "type_base_spacing": "25",
    "type_base_size": 16,
    "type_base_line_height": 1.6,
    "type_navigation_style": "body",
    "type_navigation_size": 16,
    "type_navigation_capitalize": false,
    "text_direction": "ltr",
    "button_style": "square",
    "cart_icon": "bag-minimal",
    "icon_weight": "3px",
    "icon_linecaps": "miter",
    "superscript_decimals": true,
    "vendor_enable": true,
    "quick_shop_enable": true,
    "quick_add_enable": true,
    "product_grid_image_size": "square",
    "product_grid_image_fill": false,
    "product_hover_image": true,
    "enable_swatches": true,
    "product_grid_style": "simple",
    "product_grid_image_margin": 10,
    "recently_viewed_products_per_row": 5,
    "collection_grid_shape": "square",
    "collection_grid_image_fill": false,
    "collection_grid_image": "product",
    "collection_grid_image_margin": 15,
    "cart_type": "dropdown",
    "cart_additional_buttons": false,
    "cart_collection": "savanna-collections-furniture",
    "cart_notes_enable": false,
    "cart_terms_conditions_enable": false,
    "social_facebook_link": "https://www.facebook.com/profile.php?id=100063806004977",
    "social_pinterest_link": "https://www.pinterest.com/Sicotasofficial/",
    "social_instagram_link": "https://www.instagram.com/sicotas/",
    "social_tiktok_link": "https://www.tiktok.com/@sicotas",
    "social_youtube_link": "https://www.youtube.com/channel/UCCn0Sd5HymHpHL64UNhYK3w",
    "favicon": "shopify://shop_images/20250103-110852.jpg",
    "show_breadcrumbs": false,
    "show_breadcrumbs_collection_link": false,
    "checkout_logo_position": "left",
    "checkout_logo_size": "small",
    "checkout_body_background_color": "#ffffff",
    "checkout_input_background_color_mode": "white",
    "checkout_sidebar_background_color": "#fafafa",
    "checkout_accent_color": "#111111",
    "checkout_button_color": "#111111",
    "checkout_error_color": "#ff6d6d",
    "type_product_style": "body",
    "type_product_capitalize": true,
    "color_image_overlay": "#000000",
    "color_image_overlay_opacity": 0,
    "color_image_overlay_text_shadow": 28,
    "type_headers_align_text": false,
    "type_body_align_text": false,
    "color_drawer_background": "#ffffff",
    "color_drawer_text": "#000000",
    "color_drawer_border": "#e8e8e1",
    "color_drawer_button": "#111111",
    "color_drawer_button_text": "#ffffff",
    "color_modal_overlays": "#e6e6e6",
    "type_collection_font": "body",
    "type_collection_size": 18,
    "collection_grid_style": "below",
    "collection_grid_text_align": "center",
    "collection_grid_tint": "#000000",
    "collection_grid_opacity": 10,
    "collection_grid_gutter": true,
    "breadcrumb": false,
    "sections": {
      "collection-header": {
        "type": "collection-header",
        "settings": {
          "enable": true,
          "collection_image_enable": true,
          "parallax": false
        }
      },
      "product-recommendations": {
        "type": "product-recommendations",
        "settings": {
          "product_recommendations_heading": "You may also like"
        }
      },
      "recently-viewed": {
        "type": "recently-viewed",
        "settings": {}
      },
      "password-header": {
        "type": "password-header",
        "settings": {
          "desktop_logo_height": 100,
          "mobile_logo_height": 60
        }
      },
      "ecom-footer-footer": {
        "type": "ecom-footer-footer",
        "settings": {}
      }
    },
    "content_for_index": [],
    "blocks": {
      "12120936241911852800": {
        "type": "shopify://apps/judge-me-reviews/blocks/reviews_tab/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8",
        "disabled": true,
        "settings": {
          "position": "right"
        }
      },
      "11724500921838622758": {
        "type": "shopify://apps/frequently-bought/blocks/app-embed-block/b1a8cbea-c844-4842-9529-7c62dbab1b1f",
        "disabled": true,
        "settings": {}
      },
      "1592368672439559791": {
        "type": "shopify://apps/bixgrow-affiliate/blocks/toolbar/b4f8e2e0-5ee6-49dd-b333-2e5542feb60a",
        "disabled": true,
        "settings": {}
      },
      "9859414570452352049": {
        "type": "shopify://apps/bixgrow-affiliate/blocks/bixgrow_tracker/b4f8e2e0-5ee6-49dd-b333-2e5542feb60a",
        "disabled": true,
        "settings": {}
      },
      "234434759646961565": {
        "type": "shopify://apps/bixgrow-affiliate/blocks/widget/b4f8e2e0-5ee6-49dd-b333-2e5542feb60a",
        "disabled": true,
        "settings": {}
      },
      "13786924369225772985": {
        "type": "shopify://apps/c-hub-customer-accounts/blocks/accounts-everywhere/0fb3ba0b-4c65-4919-a85a-48bc2d368e9c",
        "disabled": true,
        "settings": {}
      },
      "15181020662106344915": {
        "type": "shopify://apps/judge-me-reviews/blocks/popup_widget/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8",
        "disabled": true,
        "settings": {}
      },
      "14060646107696862457": {
        "type": "shopify://apps/judge-me-reviews/blocks/judgeme_core/61ccd3b1-a9f2-4160-9fe9-4fec8413e5d8",
        "disabled": true,
        "settings": {}
      },
      "5846654113244447633": {
        "type": "shopify://apps/xo-scroll-to-top/blocks/xo-scrolltop/ea52da47-6aa9-474c-8ff9-df181b3784b4",
        "disabled": true,
        "settings": {}
      },
      "3838990611052461119": {
        "type": "shopify://apps/c-hub-customer-accounts/blocks/app-embed/0fb3ba0b-4c65-4919-a85a-48bc2d368e9c",
        "disabled": true,
        "settings": {
          "account_primary_page": "",
          "main_element": "",
          "custom_element": "",
          "page_element": "",
          "proxy_element": "",
          "mount_json_code": ""
        }
      },
      "21474836472147483647": {
        "type": "shopify://apps/ecomposer-builder/blocks/app-embed/a0fc26e1-7741-4773-8b27-39389b4fb4a0",
        "disabled": true,
        "settings": {}
      },
      "6279071938380501511": {
        "type": "shopify://apps/powerful-form-builder/blocks/app-embed/e4bcb1eb-35b2-42e6-bc37-bfe0e1542c9d",
        "disabled": true,
        "settings": {}
      },
      "6289495341927022121": {
        "type": "shopify://apps/ymq-cart-drawer-cart-upsell/blocks/app-embed/998af8a7-571f-419f-b29a-2dd178cb5578",
        "disabled": true,
        "settings": {}
      },
      "13768625480086291342": {
        "type": "shopify://apps/forms/blocks/forms/8744a304-fcb1-4347-b211-bb6b4759a76a",
        "disabled": true,
        "settings": {}
      },
      "************08962911": {
        "type": "shopify://apps/oxi-social-login/blocks/social-login-embed/24ad60bc-8f09-42fa-807e-e5eda0fdae17",
        "disabled": true,
        "settings": {
          "enabled_on_page": "all"
        }
      },
      "16237003337219470499": {
        "type": "shopify://apps/appstle-loyalty/blocks/appstle-loyalty/1886c0f8-77b6-4a85-af11-b43cc17ae1c1",
        "disabled": true,
        "settings": {}
      },
      "643381569867433295": {
        "type": "shopify://apps/infinite-fb-tiktok-pixels/blocks/app-embed/66ae554b-dfd3-49ce-89e3-201f6854e21d",
        "disabled": true,
        "settings": {}
      },
      "6146500278089136509": {
        "type": "shopify://apps/bm-country-blocker-ip-blocker/blocks/boostmark-blocker/bf9db4b9-be4b-45e1-8127-bbcc07d93e7e",
        "disabled": true,
        "settings": {}
      },
      "1232542678405107163": {
        "type": "shopify://apps/instafeed/blocks/head-block/c447db20-095d-4a10-9725-b5977662c9d5",
        "disabled": true,
        "settings": {}
      },
      "4644077978495408606": {
        "type": "shopify://apps/appstle-memberships/blocks/appstle-membership-helper/76528d70-5fbe-48f8-8e19-590d109aa2b7",
        "disabled": true,
        "settings": {}
      },
      "15683396631634586217": {
        "type": "shopify://apps/inbox/blocks/chat/841fc607-4181-4ad1-842d-e24d7f8bad6b",
        "disabled": true,
        "settings": {
          "button_color": "#6f5034",
          "secondary_color": "#ffffff",
          "ternary_color": "#6a6a6a",
          "button_icon": "chat_bubble",
          "button_text": "no_text",
          "button_horizontal_position": "bottom_right",
          "button_vertical_position": "lowest",
          "greeting_message": ""
        }
      }
    }
  },
  "presets": {
    "Modern": {
      "color_body_bg": "#ffffff",
      "color_body_text": "#000000",
      "color_price": "#1c1d1d",
      "color_savings_text": "#ba4444",
      "color_borders": "#e8e8e1",
      "color_button": "#111111",
      "color_button_text": "#ffffff",
      "color_sale_tag": "#ba4444",
      "color_cart_dot": "#d75d43",
      "color_small_image_bg": "#ffffff",
      "color_large_image_bg": "#0f0f0f",
      "color_header": "#111212",
      "color_header_text": "#ffffff",
      "color_announcement": "#cc8f82",
      "color_announcement_text": "#ffffff",
      "color_header_search": "#ffffff",
      "color_footer": "#ffffff",
      "color_footer_border": "#e8e8e1",
      "color_footer_text": "#000000",
      "color_scheme_1_bg": "#d75d43",
      "color_scheme_1_text": "#ffffff",
      "color_scheme_1_texture": "wave",
      "color_scheme_2_bg": "#f4e3da",
      "color_scheme_2_text": "#000000",
      "color_scheme_2_texture": "wave",
      "color_scheme_3_bg": "#fcf4f1",
      "color_scheme_3_text": "#000000",
      "color_scheme_3_texture": "wave",
      "type_header_spacing": "25",
      "type_header_base_size": 35,
      "type_header_line_height": 1.1,
      "type_header_capitalize": false,
      "type_base_spacing": "25",
      "type_base_size": 16,
      "type_base_line_height": 1.6,
      "type_navigation_style": "body",
      "type_navigation_size": 13,
      "type_navigation_capitalize": true,
      "button_style": "square",
      "cart_icon": "bag-minimal",
      "icon_weight": "3px",
      "icon_linecaps": "miter",
      "superscript_decimals": true,
      "vendor_enable": true,
      "quick_shop_enable": true,
      "quick_add_enable": true,
      "product_grid_image_size": "square",
      "product_grid_image_fill": false,
      "product_hover_image": true,
      "product_grid_style": "simple",
      "product_grid_image_margin": 10,
      "recently_viewed_products_per_row": 5,
      "collection_grid_shape": "circle",
      "collection_grid_image": "product",
      "collection_grid_image_margin": 15,
      "cart_type": "dropdown",
      "cart_additional_buttons": false,
      "cart_notes_enable": false,
      "cart_terms_conditions_enable": false,
      "social_facebook_link": "https://www.facebook.com/shopify",
      "social_instagram_link": "https://instagram.com/shopify",
      "show_breadcrumbs": false,
      "show_breadcrumbs_collection_link": true,
      "checkout_logo_position": "left",
      "checkout_logo_size": "small",
      "checkout_body_background_color": "#ffffff",
      "checkout_input_background_color_mode": "white",
      "checkout_sidebar_background_color": "#fafafa",
      "checkout_accent_color": "#111111",
      "checkout_button_color": "#111111",
      "checkout_error_color": "#ff6d6d",
      "type_product_style": "body",
      "type_product_capitalize": true,
      "color_image_overlay": "#000000",
      "color_image_overlay_opacity": 0,
      "color_image_overlay_text_shadow": 28,
      "type_headers_align_text": false,
      "type_body_align_text": false,
      "color_drawer_background": "#ffffff",
      "color_drawer_text": "#000000",
      "color_drawer_border": "#e8e8e1",
      "color_drawer_button": "#111111",
      "color_drawer_button_text": "#ffffff",
      "color_modal_overlays": "#e6e6e6",
      "type_collection_font": "body",
      "type_collection_size": 18,
      "collection_grid_style": "below",
      "collection_grid_text_align": "center",
      "collection_grid_tint": "#000000",
      "collection_grid_opacity": 10,
      "collection_grid_gutter": true,
      "breadcrumb": false,
      "sections": {
        "collection-header": {
          "type": "collection-header",
          "settings": {
            "enable": true,
            "collection_image_enable": true,
            "parallax": false
          }
        },
        "product-recommendations": {
          "type": "product-recommendations",
          "settings": {
            "product_recommendations_heading": "You may also like"
          }
        },
        "recently-viewed": {
          "type": "recently-viewed",
          "settings": {}
        },
        "password-header": {
          "type": "password-header",
          "settings": {
            "desktop_logo_height": 100,
            "mobile_logo_height": 60
          }
        }
      },
      "content_for_index": [],
      "blocks": {
        "21474836472147483647": {
          "type": "shopify://apps/ecomposer-landing-page-builder/blocks/app-embed/a0fc26e1-7741-4773-8b27-39389b4fb4a0",
          "disabled": false,
          "settings": {}
        }
      }
    },
    "Classic": {
      "color_body_bg": "#ffffff",
      "color_body_text": "#42434d",
      "color_price": "#272831",
      "color_savings_text": "#ba4444",
      "color_borders": "#efebeb",
      "color_button": "#313652",
      "color_button_text": "#ffffff",
      "color_sale_tag": "#ba4444",
      "color_cart_dot": "#ffffff",
      "color_cart_dot_text": "#42434d",
      "color_small_image_bg": "#ffffff",
      "color_large_image_bg": "#e7e7e7",
      "color_header": "#383a46",
      "color_header_text": "#ffffff",
      "color_announcement": "#272831",
      "color_announcement_text": "#ffffff",
      "color_header_search": "#4b4d5e",
      "color_footer": "#f9f9fb",
      "color_footer_border": "#e8e8e1",
      "color_footer_text": "#272831",
      "color_scheme_1_bg": "#d3dac9",
      "color_scheme_1_text": "#272831",
      "color_scheme_1_texture": "none",
      "color_scheme_2_bg": "#343643",
      "color_scheme_2_text": "#ffffff",
      "color_scheme_2_texture": "notebook",
      "color_scheme_3_bg": "#efebeb",
      "color_scheme_3_text": "#272831",
      "color_scheme_3_texture": "none",
      "type_header_font_family": "figtree_n5",
      "type_header_spacing": "0",
      "type_header_base_size": 42,
      "type_header_line_height": 1.2,
      "type_header_capitalize": false,
      "type_base_font_family": "figtree_n4",
      "type_base_spacing": "25",
      "type_base_size": 14,
      "type_base_line_height": 1.5,
      "type_navigation_style": "body",
      "type_navigation_size": 18,
      "type_navigation_capitalize": false,
      "edges": "round",
      "button_style": "round-slight",
      "cart_icon": "cart",
      "icon_weight": "3px",
      "icon_linecaps": "round",
      "superscript_decimals": true,
      "vendor_enable": true,
      "quick_shop_enable": true,
      "quick_add_enable": true,
      "product_grid_image_size": "square",
      "product_grid_image_fill": true,
      "product_hover_image": true,
      "enable_swatches": true,
      "product_grid_style": "grey-round",
      "product_grid_image_margin": 10,
      "collection_grid_shape": "circle",
      "collection_grid_image": "product",
      "collection_grid_image_margin": 17,
      "cart_type": "dropdown",
      "cart_additional_buttons": false,
      "cart_collection": "business-card-cases",
      "cart_notes_enable": false,
      "cart_terms_conditions_enable": false,
      "social_facebook_link": "https://www.facebook.com/shopify",
      "social_pinterest_link": "https://www.pinterest.com/shopify",
      "social_instagram_link": "https://instagram.com/shopify",
      "social_tiktok_link": "https://www.tiktok.com/@shopify",
      "share_facebook": true,
      "share_twitter": true,
      "share_pinterest": true,
      "favicon": "shopify://shop_images/favicon-u.png",
      "show_breadcrumbs": false,
      "show_breadcrumbs_collection_link": true,
      "checkout_logo_position": "left",
      "checkout_logo_size": "small",
      "checkout_body_background_color": "#ffffff",
      "checkout_input_background_color_mode": "white",
      "checkout_sidebar_background_color": "#fafafa",
      "checkout_accent_color": "#111111",
      "checkout_button_color": "#111111",
      "checkout_error_color": "#ff6d6d",
      "search_top_collection": "cocktail-style",
      "sku_enable": true,
      "product_zoom_enable": true,
      "collection_color_swatches": true,
      "variant_type": "button",
      "enable_payment_button": true,
      "quantity_enable": false,
      "inventory_enable": true,
      "inventory_transfers_enable": true,
      "surface_pickup_enable": true,
      "sales_point_3": "Carbon neutral",
      "product_color_swatches": true,
      "product_content_1": "shipping-info",
      "product_contact": true,
      "predictive_search_show_vendor": true,
      "predictive_search_show_price": true,
      "type_product_style": "body",
      "type_product_capitalize": true,
      "color_image_overlay": "#000000",
      "color_image_overlay_opacity": 0,
      "color_image_overlay_text_shadow": 28,
      "type_headers_align_text": false,
      "type_body_align_text": true,
      "color_drawer_background": "#ffffff",
      "color_drawer_text": "#000000",
      "color_drawer_border": "#e8e8e1",
      "color_drawer_button": "#111111",
      "color_drawer_button_text": "#ffffff",
      "color_modal_overlays": "#e6e6e6",
      "type_collection_font": "body",
      "type_collection_size": 18,
      "collection_grid_style": "below",
      "collection_grid_text_align": "center",
      "collection_grid_tint": "#000000",
      "collection_grid_opacity": 10,
      "collection_grid_gutter": true,
      "breadcrumb": false,
      "sections": {
        "header": {
          "type": "header",
          "blocks": {
            "1524770014057": {
              "type": "logo",
              "settings": {
                "logo": "shopify://shop_images/urban_living-white.png",
                "logo-inverted": "shopify://shop_images/urban_living-white.png",
                "desktop_logo_width": 120,
                "mobile_logo_width": 110
              }
            }
          },
          "block_order": [
            "1524770014057"
          ],
          "settings": {
            "main_menu_link_list": "main-menu",
            "main_menu_alignment": "below",
            "mega_products": true,
            "header_sticky": true,
            "header_footer_menu": true,
            "sticky_index": false,
            "sticky_collection": false
          }
        },
        "article-template": {
          "type": "article-template",
          "settings": {
            "blog_capitalize_first": true,
            "blog_show_tags": true,
            "blog_show_date": true,
            "blog_show_comments": true,
            "blog_show_author": true
          }
        },
        "blog-template": {
          "type": "blog-template",
          "settings": {
            "blog_show_rss": false,
            "blog_show_tags": true,
            "blog_show_date": true,
            "blog_show_comments": true,
            "blog_show_author": false,
            "blog_image_size": "wide"
          }
        },
        "1499789718857": {
          "type": "featured-collections",
          "blocks": {
            "1499789718857-0": {
              "type": "collection",
              "settings": {
                "collection": "furniture",
                "title": ""
              }
            },
            "deff6d8f-5200-4686-b6aa-bcfff90ddeef": {
              "type": "collection",
              "settings": {
                "collection": "planters",
                "title": ""
              }
            },
            "1499789718857-1": {
              "type": "collection",
              "settings": {
                "collection": "outdoor-garden-decor",
                "title": "Outdoor & Garden"
              }
            },
            "1499789718857-2": {
              "type": "collection",
              "settings": {
                "collection": "bergo-kids",
                "title": "Kids"
              }
            },
            "1499789718857-3": {
              "type": "collection",
              "settings": {
                "collection": "coffee-accessories",
                "title": ""
              }
            },
            "e2697eb6-4ee1-4531-8beb-f000e64b262c": {
              "type": "collection",
              "settings": {
                "collection": "knives",
                "title": ""
              }
            },
            "bdee223b-343e-40da-97f5-d2f936098982": {
              "type": "collection",
              "settings": {
                "collection": "bookends",
                "title": ""
              }
            },
            "87af0dc3-6ea7-4a4e-a530-47a86583d1d9": {
              "type": "collection",
              "settings": {
                "collection": "canadiana",
                "title": ""
              }
            },
            "e15fd0c4-d11d-498d-8146-bf6198f0f9c4": {
              "type": "collection",
              "settings": {
                "collection": "design-icons",
                "title": ""
              }
            },
            "62c4462c-e69d-4b80-9b86-e137c4fd84cf": {
              "type": "collection",
              "settings": {
                "collection": "limited-editions",
                "title": ""
              }
            },
            "3a8d8b83-f7c4-4265-aaae-55621028d824": {
              "type": "collection",
              "settings": {
                "collection": "fun-games-puzzles",
                "title": "Games & Puzzles"
              }
            },
            "9d71d183-dc21-4253-9b92-e2eaaff7bef9": {
              "type": "collection",
              "settings": {
                "collection": "sale",
                "title": ""
              }
            }
          },
          "block_order": [
            "1499789718857-0",
            "deff6d8f-5200-4686-b6aa-bcfff90ddeef",
            "1499789718857-1",
            "1499789718857-2",
            "1499789718857-3",
            "e2697eb6-4ee1-4531-8beb-f000e64b262c",
            "bdee223b-343e-40da-97f5-d2f936098982",
            "87af0dc3-6ea7-4a4e-a530-47a86583d1d9",
            "e15fd0c4-d11d-498d-8146-bf6198f0f9c4",
            "62c4462c-e69d-4b80-9b86-e137c4fd84cf",
            "3a8d8b83-f7c4-4265-aaae-55621028d824",
            "9d71d183-dc21-4253-9b92-e2eaaff7bef9"
          ],
          "settings": {
            "title": "Top picks",
            "divider": false
          }
        },
        "newsletter-popup": {
          "type": "newsletter-popup",
          "blocks": {
            "6a2a8ea3-1f40-4a70-a4e1-66e625091344": {
              "type": "header",
              "settings": {
                "reminder_label": "Get 10% off"
              }
            }
          },
          "block_order": [
            "6a2a8ea3-1f40-4a70-a4e1-66e625091344"
          ],
          "settings": {
            "disable_for_account_holders": true,
            "popup_seconds": 5,
            "popup_days": 30,
            "popup_title": "Get 10% off your first purchase.",
            "popup_text": "<p>Sign up today and we'll send you a 10% discount code towards your first purchase. Some restrictions apply.</p>",
            "popup_image": "shopify://shop_images/office-furniture-grey-chairs.jpg",
            "image_position": "left",
            "color_scheme": "2",
            "show_social_icons": false,
            "enable_newsletter": true,
            "button_label": "",
            "button_link": ""
          }
        },
        "collection-header": {
          "type": "collection-header",
          "settings": {
            "enable": true,
            "collection_image_enable": true,
            "parallax": false
          }
        },
        "*************": {
          "type": "blog-posts",
          "settings": {
            "title": "From the journal",
            "blog": "news",
            "blog_show_tags": true,
            "blog_show_date": false,
            "blog_show_comments": false,
            "blog_show_author": false,
            "blog_image_size": "wide",
            "divider": false
          }
        },
        "1525295772132": {
          "type": "featured-collection",
          "settings": {
            "title": "Tables and seating",
            "home_featured_products": "furniture",
            "count": 5,
            "mobile_scrollable": true,
            "view_all": true,
            "divider": false
          }
        },
        "footer-promotions": {
          "type": "footer-promotions",
          "blocks": {
            "footer-promotions-0": {
              "type": "promotion",
              "settings": {
                "enable_image": true,
                "image": "shopify://shop_images/person-sits-on-there-balcony-and-tends-to-a-large-plant.jpg",
                "title": "Spring Savings",
                "text": "<p>We're clearing out last years inventory, shop now and save up to 40% off!</p>",
                "button_label": "Shop Sale",
                "button_link": "shopify://collections/sale",
                "color_scheme": "1"
              }
            },
            "footer-promotions-1": {
              "type": "promotion",
              "settings": {
                "enable_image": true,
                "image": "shopify://shop_images/home-decor-and-kitchen-accessories.jpg",
                "title": "New from Umbra",
                "text": "<p>Our latest addition makes unique watches and contemporary jewellery.</p>",
                "button_label": "Shop Umbra",
                "button_link": "shopify://collections/whats-new",
                "color_scheme": "3"
              }
            },
            "footer-promotions-2": {
              "type": "promotion",
              "settings": {
                "enable_image": true,
                "image": "shopify://shop_images/brief-exchange-during-curbside-pickup.jpg",
                "title": "Next-day pickup",
                "text": "<p>Mon - Fri: 10am - 7pm<br/>Sat - Sun: 11am - 6pm</p>",
                "button_label": "View locations",
                "button_link": "shopify://pages/our-story",
                "color_scheme": "2"
              }
            }
          },
          "block_order": [
            "footer-promotions-0",
            "footer-promotions-1",
            "footer-promotions-2"
          ],
          "settings": {
            "hide_homepage": false
          }
        },
        "product-recommendations": {
          "type": "product-recommendations",
          "settings": {
            "product_recommendations_heading": "You may also like"
          }
        },
        "recently-viewed": {
          "type": "recently-viewed",
          "settings": {}
        },
        "toolbar": {
          "type": "toolbar",
          "blocks": {
            "1d60d2aa-1704-4343-98b9-02b9533c2c85": {
              "type": "announcement",
              "settings": {
                "richtext": "<p>Hassle-free returns. 30-day postage paid returns</p>",
                "richtext_mobile": ""
              }
            },
            "a3c703d0-3d75-4c68-be7c-5e25e58e383d": {
              "type": "announcement",
              "settings": {
                "richtext": "<p>Up to 40% Off All Coffee Sets</p>",
                "richtext_mobile": ""
              }
            },
            "b5fedb0e-6170-46b8-9235-d5e69834337b": {
              "type": "announcement",
              "settings": {
                "richtext": "<p>Free Worldwide Shipping on All Purchases 100$ +</p>",
                "richtext_mobile": ""
              }
            }
          },
          "block_order": [
            "1d60d2aa-1704-4343-98b9-02b9533c2c85",
            "a3c703d0-3d75-4c68-be7c-5e25e58e383d",
            "b5fedb0e-6170-46b8-9235-d5e69834337b"
          ],
          "settings": {
            "announcement_center": false,
            "toolbar_social": false,
            "show_locale_selector": false,
            "show_currency_selector": false,
            "show_currency_flags": true
          }
        },
        "16154004051a1fa86e": {
          "type": "featured-collection",
          "settings": {
            "title": "Spring arrivals",
            "home_featured_products": "coffee-accessories",
            "count": 5,
            "mobile_scrollable": false,
            "view_all": true,
            "divider": false
          }
        },
        "blog-sidebar": {
          "type": "blog-sidebar",
          "blocks": {
            "ff7a549d-af1e-4e72-93c7-326f5ace6fbb": {
              "type": "article",
              "settings": {
                "article": "news/featured-property-tuscan-villa-with-modern-outdoor-dining"
              }
            },
            "a9c19dcb-3172-4927-b454-954715dfdfbc": {
              "type": "article",
              "settings": {
                "article": "news/d"
              }
            },
            "a0382939-ffb8-4144-83cc-9565ad819150": {
              "type": "article",
              "settings": {
                "article": "news/decorating-101-interior-design-basics"
              }
            }
          },
          "block_order": [
            "ff7a549d-af1e-4e72-93c7-326f5ace6fbb",
            "a9c19dcb-3172-4927-b454-954715dfdfbc",
            "a0382939-ffb8-4144-83cc-9565ad819150"
          ],
          "settings": {}
        },
        "footer": {
          "type": "footer",
          "blocks": {
            "footer-0": {
              "type": "menu",
              "settings": {
                "title": "",
                "menu": "about-us",
                "enable_account_link": false
              }
            },
            "adab04a6-e947-4f4d-986b-3ce2ecd41e03": {
              "type": "menu",
              "settings": {
                "title": "",
                "menu": "customer-support",
                "enable_account_link": true
              }
            },
            "fe6a67a0-686e-435b-a946-f28802053c97": {
              "type": "contact",
              "settings": {
                "phone": "****** 123 1234",
                "contact": "contact-us",
                "chat": "contact-us",
                "enable_social": true
              }
            },
            "c296bc3e-1d7f-4750-b993-************": {
              "type": "payment",
              "settings": {
                "show_payment_icons": true,
                "show_locale_selector": true,
                "show_currency_selector": true,
                "show_currency_flags": true
              }
            }
          },
          "block_order": [
            "footer-0",
            "adab04a6-e947-4f4d-986b-3ce2ecd41e03",
            "fe6a67a0-686e-435b-a946-f28802053c97",
            "c296bc3e-1d7f-4750-b993-************"
          ],
          "settings": {
            "show_newsletter": true,
            "newsletter_richtext": "<p>Subscribe today and get 10% off your first purchase</p>",
            "footer_main_menu": true,
            "show_copyright": true,
            "copyright_text": "",
            "policies_menu": "policies"
          }
        },
        "16166970499d200586": {
          "type": "promo-grid",
          "blocks": {
            "8fcbacfa-2b4f-4197-a9c7-d2c9cce20ef5": {
              "type": "banner",
              "settings": {
                "heading": "Free shipping over $50",
                "text": "Within the contiguous United States",
                "link": "shopify://pages/shipping-info",
                "label": "Learn more",
                "color_scheme": "2"
              }
            },
            "39d950a6-389b-415c-b815-62cfd8e07480": {
              "type": "product",
              "settings": {
                "product": "cigar-glass",
                "subheading": "Restocked",
                "heading": "A Whiskey essential",
                "textarea": "Combine the things you love with a single glass.",
                "link_label": "Shop now",
                "label": "New",
                "enable_price": true,
                "width": "50",
                "color_scheme": "1"
              }
            },
            "a6ec910f-4f19-440b-8963-596f8c2e1ca8": {
              "type": "product",
              "settings": {
                "product": "1pt-craft-cocktail-infusions",
                "subheading": "New in",
                "heading": "Mull it over",
                "textarea": "With this great mulled wine infusion kit, just in time for winter nights.",
                "link_label": "Shop now",
                "label": "",
                "enable_price": true,
                "width": "50",
                "color_scheme": "1"
              }
            }
          },
          "block_order": [
            "8fcbacfa-2b4f-4197-a9c7-d2c9cce20ef5",
            "39d950a6-389b-415c-b815-62cfd8e07480",
            "a6ec910f-4f19-440b-8963-596f8c2e1ca8"
          ],
          "settings": {
            "full_width": false,
            "gutter_size": 20,
            "space_above": false,
            "space_below": false
          }
        },
        "16167068142513abfa": {
          "type": "logo-list",
          "blocks": {
            "16167068142513abfa-0": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/alessi.png",
                "link": "/collections/vendors?q=Alessi"
              }
            },
            "16167068142513abfa-1": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/bella-tunno_30ceeb35-194d-4c26-a3ed-d29f8480800a.png",
                "link": "/collections/vendors?q=Bella Tunno"
              }
            },
            "16167068142513abfa-2": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/corkcicle.png",
                "link": "/collections/vendors?q=Corkcicle"
              }
            },
            "16167068142513abfa-3": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/eva-solo.png",
                "link": "/collections/vendors?q=Eva Solo"
              }
            },
            "052533e5-a371-4932-a016-34def42383db": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/georg-jensen.png",
                "link": "/collections/vendors?q=Georg Jensen"
              }
            },
            "ba0ef081-a41c-4913-a3db-412ecb522875": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/iittala.png",
                "link": "/collections/vendors?q=iittala"
              }
            },
            "28f0d3a8-34e2-4ab3-aed2-005079fb606c": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/jonathan-adler.jpg",
                "link": "/collections/vendors?q=Jonathan Adler"
              }
            },
            "9a3e96b5-aa08-4445-9220-c9d3f38ebb1f": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/kikkerland.png",
                "link": "/collections/vendors?q=Kikkerland"
              }
            },
            "a8344f31-4814-45bb-bcbe-344add4a8906": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/moleskine.jpg",
                "link": "/collections/vendors?q=Moleskine"
              }
            },
            "68c87a86-7a8c-4bfe-ae97-6e35be8bd1e1": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/moma_f69c6feb-0ca4-4296-8177-e485c100b3ea.png",
                "link": "/collections/vendors?q=MoMA"
              }
            },
            "3220078b-da32-490e-aad7-343808280226": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/tom-dixon.png",
                "link": "/collections/vendors?q=Tom Dixon"
              }
            },
            "11926c01-2a95-4f6f-b2fc-42255d8237e5": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/umbra_cc0f54b2-3c63-448c-a203-872e882c06c6.png",
                "link": "/collections/vendors?q=Umbra"
              }
            }
          },
          "block_order": [
            "16167068142513abfa-0",
            "16167068142513abfa-1",
            "16167068142513abfa-2",
            "16167068142513abfa-3",
            "052533e5-a371-4932-a016-34def42383db",
            "ba0ef081-a41c-4913-a3db-412ecb522875",
            "28f0d3a8-34e2-4ab3-aed2-005079fb606c",
            "9a3e96b5-aa08-4445-9220-c9d3f38ebb1f",
            "a8344f31-4814-45bb-bcbe-344add4a8906",
            "68c87a86-7a8c-4bfe-ae97-6e35be8bd1e1",
            "3220078b-da32-490e-aad7-343808280226",
            "11926c01-2a95-4f6f-b2fc-42255d8237e5"
          ],
          "settings": {
            "title": "Popular brands",
            "divider": false
          }
        },
        "password-header": {
          "type": "password-header",
          "settings": {
            "desktop_logo_height": 100,
            "mobile_logo_height": 60
          }
        },
        "161789469679fffe6b": {
          "type": "slideshow-split",
          "blocks": {
            "161789469679fffe6b-2": {
              "type": "slide",
              "settings": {
                "top_subheading": "",
                "title": "New from Wild Wood",
                "title_size": 60,
                "subheading": "Decorative planters that will bring your home office to life.",
                "link": "shopify://collections/planters",
                "link_text": "Shop Planters",
                "link_text_2": "",
                "text_position": "right",
                "image": "shopify://shop_images/green-wall-cream.jpg",
                "indent_image": false,
                "color_scheme": "1"
              }
            },
            "161789469679fffe6b-1": {
              "type": "slide",
              "settings": {
                "top_subheading": "",
                "title": "Lakehouse Lifestyle.",
                "title_size": 60,
                "subheading": "Spring has sprung and it's time to make your outdoor space come to life. Shop all the top outdoor brands.",
                "link": "shopify://collections/homestyle",
                "link_text": "Shop Home",
                "link_2": "shopify://collections/decor",
                "link_text_2": "Shop All",
                "text_position": "left",
                "image": "shopify://shop_images/outdoor-dining_685376f1-9e94-4d09-8b14-a621a5e8e19b.jpg",
                "indent_image": false,
                "color_scheme": "2"
              }
            },
            "161789469679fffe6b-0": {
              "type": "slide",
              "settings": {
                "top_subheading": "",
                "title": "Back in stock.",
                "title_size": 70,
                "subheading": "We've restocked on items from Jonathan Adler, Tom Dixon, and more of your favourite designers.",
                "link": "/collections/vendors?q=Jonathan%20Adler",
                "link_text": "Shop New",
                "link_2": "shopify://collections/decor",
                "link_text_2": "Shop All",
                "text_position": "left",
                "image": "shopify://shop_images/product.jpg",
                "indent_image": true,
                "color_scheme": "1"
              }
            }
          },
          "block_order": [
            "161789469679fffe6b-2",
            "161789469679fffe6b-1",
            "161789469679fffe6b-0"
          ],
          "settings": {
            "full_width": true,
            "height": 600,
            "height_mobile": 450,
            "style": "arrows",
            "autoplay": true,
            "autoplay_speed": 7
          }
        },
        "161789690258ed465f": {
          "type": "newsletter",
          "settings": {
            "color_scheme": "2"
          }
        },
        "1617897211fa46356d": {
          "type": "background-image-text",
          "disabled": true,
          "settings": {
            "subtitle": "",
            "title": "Serving NYC since 1994.",
            "text": "<p>Thanks to your business over the years we&#x27;ve grown to cover Brooklyn, Manhattan and Queens. Now offering next-day local pickup.</p>",
            "button_label": "View locations",
            "button_link": "shopify://pages/our-story",
            "image": "shopify://shop_images/store-expensive_6dd3bb0d-7aaf-4624-919f-4f57bc61932c.jpg",
            "layout": "left",
            "height": 550,
            "parallax": false
          }
        },
        "16194500635e2e2d35": {
          "type": "promo-grid",
          "blocks": {
            "95ee7881-6f87-497a-8b37-65a8307e81de": {
              "type": "sale_collection",
              "settings": {
                "sale_collection": "coffee-time",
                "top_text": "Up to",
                "middle_text": "20% off",
                "bottom_text": "Coffee accessories",
                "width": "50",
                "color_scheme": "3"
              }
            },
            "58061451-10b9-4cc7-95b8-f03131795311": {
              "type": "sale_collection",
              "settings": {
                "sale_collection": "cutlery",
                "top_text": "Up to",
                "middle_text": "15% off",
                "bottom_text": "Select cutlery",
                "width": "50",
                "color_scheme": "3"
              }
            }
          },
          "block_order": [
            "95ee7881-6f87-497a-8b37-65a8307e81de",
            "58061451-10b9-4cc7-95b8-f03131795311"
          ],
          "settings": {
            "full_width": false,
            "gutter_size": 20,
            "space_above": false,
            "space_below": false
          }
        },
        "1623090756d994291c": {
          "type": "slideshow",
          "blocks": {
            "1623090756d994291c-0": {
              "type": "hero",
              "settings": {
                "top_subheading": "",
                "title": "<p>Build your dream <br/>outdoor oasis.</p>",
                "title_size": 56,
                "subheading": "",
                "link": "shopify://collections/outdoor-garden-decor",
                "link_text": "Shop Outdoor",
                "link_2": "shopify://collections/decor",
                "link_text_2": "Shop All",
                "color_accent": "#ffffff",
                "text_align": "vertical-center horizontal-center",
                "image": "shopify://shop_images/outdoor-pool_cf604255-b274-4f3c-a917-4fd383330216.jpg",
                "overlay_opacity": 0
              }
            },
            "1623090756d994291c-1": {
              "type": "hero",
              "settings": {
                "top_subheading": "",
                "title": "<p>Entertain in style.</p>",
                "title_size": 55,
                "subheading": "",
                "link": "shopify://collections/outdoor-style",
                "link_text": "Shop Outdoor",
                "link_2": "shopify://collections/decor",
                "link_text_2": "Shop All",
                "color_accent": "#ffffff",
                "text_align": "vertical-center horizontal-center",
                "image": "shopify://shop_images/outdoor-dining_842be956-7149-4cee-bf47-2ed9d99bbb19.jpg",
                "overlay_opacity": 0
              }
            }
          },
          "block_order": [
            "1623090756d994291c-0",
            "1623090756d994291c-1"
          ],
          "settings": {
            "full_width": true,
            "desktop_size": -10,
            "mobile_size": 0,
            "style": "arrows",
            "autoplay": true,
            "autoplay_speed": 5
          }
        },
        "16242877764ba69f0b": {
          "type": "slideshow-split",
          "blocks": {
            "16242877764ba69f0b-0": {
              "type": "slide",
              "settings": {
                "top_subheading": "",
                "title": "Serving NYC since 1994.",
                "title_size": 45,
                "subheading": "Thanks to your business over the years we've grown to cover Brooklyn, Manhattan and Queens. Now offering next-day local pickup.",
                "link": "shopify://pages/our-story",
                "link_text": "View locations",
                "link_text_2": "",
                "text_position": "left",
                "image": "shopify://shop_images/store-expensive_6dd3bb0d-7aaf-4624-919f-4f57bc61932c.jpg",
                "indent_image": false,
                "color_scheme": "1"
              }
            }
          },
          "block_order": [
            "16242877764ba69f0b-0"
          ],
          "settings": {
            "full_width": true,
            "height": 550,
            "height_mobile": 450,
            "style": "arrows",
            "autoplay": true,
            "autoplay_speed": 7
          }
        },
        "age-verification-popup": {
          "type": "age-verification-popup",
          "settings": {
            "enable_test_mode": false,
            "color_scheme": "none",
            "blur_image": false,
            "heading": "Confirm your age",
            "text": "<p>Are you 18 years old or older?</p>",
            "decline_button_label": "No I'm not",
            "approve_button_label": "Yes I am",
            "decline_heading": "Come back when you're older",
            "decline_text": "<p>Sorry, the content of this store can't be seen by a younger audience. Come back when you're older.</p>",
            "return_button_label": "Oops, I entered incorrectly"
          }
        }
      },
      "content_for_index": [
        "161789469679fffe6b",
        "1499789718857",
        "16194500635e2e2d35",
        "1525295772132",
        "1623090756d994291c",
        "16154004051a1fa86e",
        "16166970499d200586",
        "16167068142513abfa",
        "161789690258ed465f",
        "*************",
        "1617897211fa46356d",
        "16242877764ba69f0b"
      ]
    },
    "Contemporary": {
      "color_body_bg": "#ffffff",
      "color_body_text": "#1c1c1c",
      "color_price": "#1c1d1d",
      "color_savings_text": "#ba4444",
      "color_borders": "#e8e8e1",
      "color_button": "#000000",
      "color_button_text": "#ffffff",
      "color_sale_tag": "#ba4444",
      "color_cart_dot": "#ba4444",
      "color_small_image_bg": "#ffffff",
      "color_large_image_bg": "#e7e7e7",
      "color_header": "#ffffff",
      "color_header_text": "#000000",
      "color_announcement": "#f1f5d5",
      "color_announcement_text": "#536524",
      "color_header_search": "#f7f7f7",
      "color_footer": "#ffffff",
      "color_footer_border": "#e8e8e1",
      "color_footer_text": "#242430",
      "color_scheme_1_bg": "#dbded0",
      "color_scheme_1_text": "#000000",
      "color_scheme_1_texture": "none",
      "color_scheme_2_bg": "#f1f5d5",
      "color_scheme_2_text": "#1c1c1c",
      "color_scheme_2_texture": "none",
      "color_scheme_3_bg": "#616524",
      "color_scheme_3_text": "#f7f5f1",
      "color_scheme_3_texture": "minimal-wave",
      "type_header_font_family": "trirong_n4",
      "type_header_spacing": "0",
      "type_header_base_size": 50,
      "type_header_line_height": 1.1,
      "type_header_capitalize": false,
      "type_base_font_family": "sans-serif",
      "type_base_spacing": "25",
      "type_base_size": 16,
      "type_base_line_height": 1.5,
      "type_navigation_style": "body",
      "type_navigation_size": 14,
      "type_navigation_capitalize": true,
      "edges": "square",
      "button_style": "square",
      "cart_icon": "cart",
      "icon_weight": "4px",
      "icon_linecaps": "miter",
      "superscript_decimals": true,
      "vendor_enable": true,
      "quick_shop_enable": true,
      "quick_add_enable": true,
      "product_grid_image_size": "square",
      "product_grid_image_fill": true,
      "product_hover_image": true,
      "product_grid_style": "gridlines-thin",
      "product_grid_image_margin": 5,
      "recently_viewed_products_per_row": 5,
      "collection_grid_shape": "portrait",
      "collection_grid_image": "collection",
      "collection_grid_image_margin": 4,
      "cart_type": "dropdown",
      "cart_additional_buttons": false,
      "cart_collection": "all",
      "cart_notes_enable": false,
      "cart_terms_conditions_enable": false,
      "social_facebook_link": "https://www.facebook.com/shopify",
      "social_pinterest_link": "https://www.pinterest.com/shopify",
      "social_instagram_link": "https://instagram.com/shopify",
      "social_tiktok_link": "https://www.tiktok.com/@shopify",
      "share_facebook": true,
      "share_twitter": true,
      "share_pinterest": true,
      "favicon": "shopify://shop_images/favicon.png",
      "predictive_search_enabled": true,
      "show_breadcrumbs": true,
      "show_breadcrumbs_collection_link": true,
      "checkout_logo_position": "left",
      "checkout_logo_size": "small",
      "checkout_body_background_color": "#ffffff",
      "checkout_input_background_color_mode": "white",
      "checkout_sidebar_background_color": "#fafafa",
      "checkout_accent_color": "#111111",
      "checkout_button_color": "#111111",
      "checkout_error_color": "#ff6d6d",
      "search_top_collection": "all",
      "sku_enable": true,
      "product_zoom_enable": true,
      "collection_color_swatches": true,
      "variant_type": "button",
      "enable_payment_button": true,
      "quantity_enable": false,
      "inventory_enable": true,
      "inventory_transfers_enable": true,
      "surface_pickup_enable": true,
      "sales_point_1": "Free worldwide shipping",
      "sales_point_2_icon": "checkmark",
      "sales_point_2": "Alcohol-free",
      "sales_point_3": "Organic ingredients",
      "product_color_swatches": true,
      "product_contact": false,
      "color_image_overlay": "#000000",
      "color_image_overlay_opacity": 0,
      "color_image_overlay_text_shadow": 28,
      "type_headers_align_text": false,
      "type_body_align_text": true,
      "color_drawer_background": "#ffffff",
      "color_drawer_text": "#000000",
      "color_drawer_border": "#e8e8e1",
      "color_drawer_button": "#111111",
      "color_drawer_button_text": "#ffffff",
      "color_modal_overlays": "#e6e6e6",
      "type_collection_font": "body",
      "type_collection_size": 18,
      "collection_grid_style": "below",
      "collection_grid_text_align": "center",
      "collection_grid_tint": "#000000",
      "collection_grid_opacity": 10,
      "collection_grid_gutter": true,
      "breadcrumb": false,
      "sections": {
        "header": {
          "type": "header",
          "blocks": {
            "1524770014057": {
              "type": "logo",
              "settings": {
                "logo": "shopify://shop_images/Bubble.png",
                "logo-inverted": "shopify://shop_images/Frame_23-w.png",
                "desktop_logo_width": 100,
                "mobile_logo_width": 90
              }
            }
          },
          "block_order": [
            "1524770014057"
          ],
          "settings": {
            "main_menu_link_list": "main-menu",
            "main_menu_alignment": "below",
            "mega_products": true,
            "header_sticky": true,
            "header_footer_menu": true,
            "sticky_index": false,
            "sticky_collection": false
          }
        },
        "article-template": {
          "type": "article-template",
          "settings": {
            "blog_capitalize_first": true,
            "blog_show_tags": true,
            "blog_show_date": true,
            "blog_show_comments": true,
            "blog_show_author": true
          }
        },
        "blog-template": {
          "type": "blog-template",
          "settings": {
            "blog_show_rss": false,
            "blog_show_tags": true,
            "blog_show_date": true,
            "blog_show_comments": true,
            "blog_show_author": false,
            "blog_image_size": "wide"
          }
        },
        "1499789718857": {
          "type": "featured-collections",
          "blocks": {
            "deff6d8f-5200-4686-b6aa-bcfff90ddeef": {
              "type": "collection",
              "settings": {
                "collection": "non-alcoholic-cocktail",
                "title": "Cocktails"
              }
            },
            "1499789718857-1": {
              "type": "collection",
              "settings": {
                "collection": "non-alcoholic-wine",
                "title": "Wine"
              }
            },
            "1499789718857-2": {
              "type": "collection",
              "settings": {
                "collection": "non-alcoholic-beer",
                "title": "Beer"
              }
            },
            "e2697eb6-4ee1-4531-8beb-f000e64b262c": {
              "type": "collection",
              "settings": {
                "collection": "sparkling-juice",
                "title": ""
              }
            },
            "1499789718857-0": {
              "type": "collection",
              "settings": {
                "collection": "ginger-beer",
                "title": ""
              }
            },
            "1499789718857-3": {
              "type": "collection",
              "settings": {
                "collection": "drink-sets",
                "title": ""
              }
            }
          },
          "block_order": [
            "deff6d8f-5200-4686-b6aa-bcfff90ddeef",
            "1499789718857-1",
            "1499789718857-2",
            "e2697eb6-4ee1-4531-8beb-f000e64b262c",
            "1499789718857-0",
            "1499789718857-3"
          ],
          "settings": {
            "title": "All your favourites",
            "divider": false
          }
        },
        "newsletter-popup": {
          "type": "newsletter-popup",
          "blocks": {
            "89af8827-ae1b-4a50-9436-ebdfb8553fd5": {
              "type": "header",
              "settings": {
                "reminder_label": "Get 10% off"
              }
            }
          },
          "block_order": [
            "89af8827-ae1b-4a50-9436-ebdfb8553fd5"
          ],
          "settings": {
            "disable_for_account_holders": true,
            "popup_seconds": 6,
            "popup_days": 30,
            "popup_title": "Get 10% off your first purchase.",
            "popup_text": "<p>Sign up today and we'll send you a 10% discount code towards your first purchase. Some restrictions apply.</p>",
            "popup_image": "shopify://shop_images/Everleaf_3bottles_1.jpg",
            "image_position": "left",
            "color_scheme": "none",
            "show_social_icons": false,
            "enable_newsletter": true,
            "button_label": "",
            "button_link": ""
          }
        },
        "collection-header": {
          "type": "collection-header",
          "settings": {
            "enable": true,
            "collection_image_enable": true,
            "parallax": false
          }
        },
        "*************": {
          "type": "blog-posts",
          "settings": {
            "title": "Signature recipes",
            "blog": "news",
            "blog_show_tags": true,
            "blog_show_date": true,
            "blog_show_comments": false,
            "blog_show_author": false,
            "blog_image_size": "landscape",
            "divider": false
          }
        },
        "1525295772132": {
          "type": "featured-collection",
          "settings": {
            "title": "Popular picks",
            "home_featured_products": "all",
            "count": 5,
            "mobile_scrollable": true,
            "view_all": true,
            "divider": false
          }
        },
        "footer-promotions": {
          "type": "footer-promotions",
          "blocks": {
            "footer-promotions-2": {
              "type": "promotion",
              "settings": {
                "enable_image": true,
                "image": "shopify://shop_images/Leske_ALT_0521_57043_0c8cf80d-65b8-42da-a825-3ebf420ba97c.jpg",
                "title": "Organic + Sustainable.",
                "text": "<p>We started our business with a commitment to organically grown ingredients and sustainable farming practices.</p>",
                "button_label": "",
                "button_link": "shopify://pages/faq",
                "color_scheme": "3"
              }
            },
            "footer-promotions-0": {
              "type": "promotion",
              "settings": {
                "enable_image": true,
                "image": "shopify://shop_images/Everleaf_3bottles_1.jpg",
                "title": "Everleaf. At last.",
                "text": "<p>Complex and balanced non-alcoholic aperitifs made with all the depth, aroma and flavour of the natural world.</p>",
                "button_label": "",
                "button_link": "shopify://collections/everleaf",
                "color_scheme": "2"
              }
            },
            "footer-promotions-1": {
              "type": "promotion",
              "settings": {
                "enable_image": true,
                "image": "shopify://shop_images/askobanner.png",
                "title": "Summer spritzers.",
                "text": "<p>With summer heating up, try our refreshing new spritzers for a mindful way to enjoy long days relaxing in the sun.</p>",
                "button_label": "Optional button",
                "button_link": "",
                "color_scheme": "1"
              }
            }
          },
          "block_order": [
            "footer-promotions-2",
            "footer-promotions-0",
            "footer-promotions-1"
          ],
          "settings": {
            "hide_homepage": false
          }
        },
        "product-recommendations": {
          "type": "product-recommendations",
          "settings": {
            "product_recommendations_heading": "You may also like"
          }
        },
        "recently-viewed": {
          "type": "recently-viewed",
          "settings": {}
        },
        "toolbar": {
          "type": "toolbar",
          "blocks": {
            "f709e6ea-d5cc-4702-adbf-e66e246fcaf1": {
              "type": "announcement",
              "settings": {
                "richtext": "<p>Hassle-free returns. 30-day postage paid returns</p>",
                "richtext_mobile": ""
              }
            }
          },
          "block_order": [
            "f709e6ea-d5cc-4702-adbf-e66e246fcaf1"
          ],
          "settings": {
            "announcement_center": false,
            "toolbar_social": false,
            "show_locale_selector": false,
            "show_currency_selector": false,
            "show_currency_flags": true
          }
        },
        "16154004051a1fa86e": {
          "type": "featured-collection",
          "settings": {
            "title": "Non-alcoholic Wine",
            "home_featured_products": "non-alcoholic-wine",
            "count": 5,
            "mobile_scrollable": true,
            "view_all": true,
            "divider": false
          }
        },
        "blog-sidebar": {
          "type": "blog-sidebar",
          "blocks": {
            "a9c19dcb-3172-4927-b454-954715dfdfbc": {
              "type": "article",
              "settings": {
                "article": "news/lyres-amaretti-sour"
              }
            },
            "ff7a549d-af1e-4e72-93c7-326f5ace6fbb": {
              "type": "article",
              "settings": {
                "article": "news/lyres-amalfi-spritz"
              }
            },
            "a0382939-ffb8-4144-83cc-9565ad819150": {
              "type": "article",
              "settings": {
                "article": "news/lyres-dark-spicy"
              }
            }
          },
          "block_order": [
            "a9c19dcb-3172-4927-b454-954715dfdfbc",
            "ff7a549d-af1e-4e72-93c7-326f5ace6fbb",
            "a0382939-ffb8-4144-83cc-9565ad819150"
          ],
          "settings": {}
        },
        "footer": {
          "type": "footer",
          "blocks": {
            "footer-0": {
              "type": "menu",
              "settings": {
                "title": "",
                "menu": "about-us",
                "enable_account_link": false
              }
            },
            "ddefd2fb-3078-4165-80b6-2ee3eb00220b": {
              "type": "menu",
              "settings": {
                "title": "",
                "menu": "customer-support",
                "enable_account_link": true
              }
            },
            "fe6a67a0-686e-435b-a946-f28802053c97": {
              "type": "contact",
              "settings": {
                "phone": "****** 123 1234",
                "contact": "contact-us",
                "chat": "contact-us",
                "enable_social": true
              }
            },
            "c296bc3e-1d7f-4750-b993-************": {
              "type": "payment",
              "settings": {
                "show_payment_icons": true,
                "show_locale_selector": true,
                "show_currency_selector": true,
                "show_currency_flags": true
              }
            }
          },
          "block_order": [
            "footer-0",
            "ddefd2fb-3078-4165-80b6-2ee3eb00220b",
            "fe6a67a0-686e-435b-a946-f28802053c97",
            "c296bc3e-1d7f-4750-b993-************"
          ],
          "settings": {
            "show_newsletter": true,
            "newsletter_richtext": "<p>Subscribe today and get 10% off your first purchase</p>",
            "footer_main_menu": true,
            "show_copyright": true,
            "copyright_text": "",
            "policies_menu": "policies"
          }
        },
        "16166970499d200586": {
          "type": "promo-grid",
          "blocks": {
            "8fcbacfa-2b4f-4197-a9c7-d2c9cce20ef5": {
              "type": "banner",
              "settings": {
                "heading": "Free worldwide shipping",
                "text": "On orders over $100, arrives in 2-7 days.",
                "label": "",
                "color_scheme": "3"
              }
            },
            "84d708d3-fa85-41cb-b184-2def6e310102": {
              "type": "sale_collection",
              "settings": {
                "sale_collection": "ish-spirits",
                "top_text": "Save",
                "middle_text": "15%",
                "bottom_text": "On ISH spirits",
                "width": "50",
                "color_scheme": "none"
              }
            },
            "ff6095f4-fe8a-452f-a564-f3df78725e16": {
              "type": "sale_collection",
              "settings": {
                "sale_collection": "luscombe",
                "top_text": "Save",
                "middle_text": "10%",
                "bottom_text": "on ginger beer",
                "width": "50",
                "color_scheme": "none"
              }
            }
          },
          "block_order": [
            "8fcbacfa-2b4f-4197-a9c7-d2c9cce20ef5",
            "84d708d3-fa85-41cb-b184-2def6e310102",
            "ff6095f4-fe8a-452f-a564-f3df78725e16"
          ],
          "settings": {
            "full_width": false,
            "gutter_size": 20,
            "space_above": true,
            "space_below": false
          }
        },
        "16167068142513abfa": {
          "type": "logo-list",
          "blocks": {
            "16167068142513abfa-0": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/lyres.png",
                "link": "shopify://collections/lyres-spirit-co"
              }
            },
            "16167068142513abfa-1": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/everleaf.png",
                "link": "shopify://collections/everleaf"
              }
            },
            "16167068142513abfa-2": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/mikkeller.png",
                "link": "shopify://collections/mikkeller"
              }
            },
            "16167068142513abfa-3": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/luscombe.png",
                "link": "shopify://collections/luscombe-drinks"
              }
            },
            "052533e5-a371-4932-a016-34def42383db": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/ish_779ce952-6f26-4334-ac40-d43537225861.png",
                "link": "shopify://collections/ish-spirits"
              }
            },
            "ba0ef081-a41c-4913-a3db-412ecb522875": {
              "type": "logo_image",
              "settings": {
                "image": "shopify://shop_images/ambijus.png",
                "link": "shopify://collections/ambijus"
              }
            }
          },
          "block_order": [
            "16167068142513abfa-0",
            "16167068142513abfa-1",
            "16167068142513abfa-2",
            "16167068142513abfa-3",
            "052533e5-a371-4932-a016-34def42383db",
            "ba0ef081-a41c-4913-a3db-412ecb522875"
          ],
          "settings": {
            "title": "Top brands",
            "divider": false
          }
        },
        "password-header": {
          "type": "password-header",
          "settings": {
            "desktop_logo_height": 100,
            "mobile_logo_height": 60
          }
        },
        "161789469679fffe6b": {
          "type": "slideshow-split",
          "blocks": {
            "161789469679fffe6b-0": {
              "type": "slide",
              "settings": {
                "top_subheading": "",
                "title": "Mocktails, done right.",
                "title_size": 80,
                "subheading": "",
                "link_text": "Shop Now",
                "link_text_2": "",
                "text_position": "right",
                "image": "shopify://shop_images/SpritzISH_Serve_2048x2048_6eb25c52-7337-4d91-ba9f-89b13ff52488.jpg",
                "indent_image": false,
                "color_scheme": "1"
              }
            },
            "95bf295f-5a93-4117-8b8d-23ef040778d9": {
              "type": "slide",
              "settings": {
                "top_subheading": "",
                "title": "Perfection, in a bottle.",
                "title_size": 70,
                "subheading": "Lyre's signature Amaretti Sour has all the taste a mindful drinker needs.",
                "link": "shopify://blogs/news/lyres-amaretti-sour",
                "link_text": "View recipe",
                "link_text_2": "",
                "text_position": "left",
                "image": "shopify://shop_images/Lyres_Amaretti_Sour_RGB_1080x1080_416a5309-5b45-44c8-b882-e7cc01ca7868.jpg",
                "indent_image": false,
                "color_scheme": "2"
              }
            }
          },
          "block_order": [
            "161789469679fffe6b-0",
            "95bf295f-5a93-4117-8b8d-23ef040778d9"
          ],
          "settings": {
            "full_width": true,
            "height": 600,
            "height_mobile": 450,
            "style": "arrows",
            "autoplay": true,
            "autoplay_speed": 7
          }
        },
        "161789690258ed465f": {
          "type": "newsletter",
          "settings": {
            "color_scheme": "1"
          }
        },
        "162396200786f8a51c": {
          "type": "slideshow-split",
          "blocks": {
            "162396200786f8a51c-0": {
              "type": "slide",
              "settings": {
                "top_subheading": "Now stocking",
                "title": "Everleaf Organic Gin",
                "title_size": 70,
                "subheading": "Complex and balanced non-alcoholic aperitifs made with all the depth, aroma and flavour of the natural world.",
                "link": "shopify://collections/everleaf",
                "link_text": "Shop Everleaf",
                "link_text_2": "",
                "text_position": "left",
                "image": "shopify://shop_images/Everleaf_3bottles_1.jpg",
                "indent_image": false,
                "color_scheme": "3"
              }
            }
          },
          "block_order": [
            "162396200786f8a51c-0"
          ],
          "settings": {
            "full_width": true,
            "height": 650,
            "height_mobile": 450,
            "style": "arrows",
            "autoplay": true,
            "autoplay_speed": 7
          }
        },
        "162402863370f49f13": {
          "type": "slideshow",
          "blocks": {
            "162402863370f49f13-0": {
              "type": "hero",
              "settings": {
                "top_subheading": "",
                "title": "<p>Drink, responsibly.</p>",
                "title_size": 80,
                "subheading": "",
                "link_text": "Our story",
                "link_2": "shopify://pages/our-story",
                "link_text_2": "",
                "color_accent": "#ffffff",
                "text_align": "vertical-center horizontal-center",
                "image": "shopify://shop_images/Leske_ALT_0521_57043_0c8cf80d-65b8-42da-a825-3ebf420ba97c.jpg",
                "overlay_opacity": 0
              }
            }
          },
          "block_order": [
            "162402863370f49f13-0"
          ],
          "settings": {
            "full_width": true,
            "desktop_size": -10,
            "mobile_size": 20,
            "style": "arrows",
            "autoplay": true,
            "autoplay_speed": 7
          }
        },
        "1624029189039bf48d": {
          "type": "promo-grid",
          "blocks": {
            "b5248e50-a97a-4824-a294-48e248088e73": {
              "type": "advanced",
              "settings": {
                "subheading": "",
                "heading": "Summer's Best",
                "textarea": "",
                "cta_text1": "",
                "cta_link1": "shopify://collections/sparkling-juice",
                "cta_text2": "",
                "image": "shopify://shop_images/SpritzISH750.png",
                "video_url": "",
                "width": "50",
                "height": 320,
                "text_align": "vertical-center horizontal-center",
                "color_accent": "#ffffff"
              }
            },
            "89c8debe-a742-468b-8902-d1bb943e064c": {
              "type": "advanced",
              "settings": {
                "subheading": "",
                "heading": "Recipes",
                "textarea": "",
                "cta_text1": "",
                "cta_link1": "shopify://blogs/news",
                "cta_text2": "",
                "image": "shopify://shop_images/luscombe_passionate.png",
                "video_url": "",
                "width": "50",
                "height": 300,
                "text_align": "vertical-center horizontal-center",
                "color_accent": "#ffffff"
              }
            }
          },
          "block_order": [
            "b5248e50-a97a-4824-a294-48e248088e73",
            "89c8debe-a742-468b-8902-d1bb943e064c"
          ],
          "settings": {
            "full_width": false,
            "gutter_size": 20,
            "space_above": false,
            "space_below": true
          }
        },
        "162404838555055802": {
          "type": "featured-collection",
          "settings": {
            "title": "New Arrivals",
            "home_featured_products": "ish-spirits",
            "count": 5,
            "mobile_scrollable": true,
            "view_all": true,
            "divider": false
          }
        },
        "1624286980ca6c2f1b": {
          "type": "testimonials",
          "blocks": {
            "1624286980ca6c2f1b-3": {
              "type": "testimonial",
              "settings": {
                "icon": "5-stars",
                "testimonial": "<p>Couldn't be happier with the service I received from this company.</p>",
                "image": "shopify://shop_images/Screen_Shot_2021-06-21_at_10.59.41_AM.png",
                "author": "Angelina S.",
                "author_info": "Montreal, QC"
              }
            },
            "1624286980ca6c2f1b-1": {
              "type": "testimonial",
              "settings": {
                "icon": "5-stars",
                "testimonial": "<p>Really went out of their way to make me feel special as a customer, highly recommend!</p>",
                "image": "shopify://shop_images/testimonial2.jpg",
                "author": "Rachel F.",
                "author_info": "Toronto, ON"
              }
            },
            "1624286980ca6c2f1b-2": {
              "type": "testimonial",
              "settings": {
                "icon": "5-stars",
                "testimonial": "<p>Fantastic drinks, great taste and I felt so much better the day after! Would recommend to anyone.</p>",
                "image": "shopify://shop_images/testimonial4.png",
                "author": "Sam R.",
                "author_info": "Brooklyn, NY"
              }
            },
            "1624286980ca6c2f1b-0": {
              "type": "testimonial",
              "settings": {
                "icon": "5-stars",
                "testimonial": "<p>Couldn't be happier with the service I received from this company.</p>",
                "image": "shopify://shop_images/testimonial1.jpg",
                "author": "James L.",
                "author_info": "Los Angeles, CA"
              }
            },
            "1624286980ca6c2f1b-4": {
              "type": "testimonial",
              "settings": {
                "icon": "5-stars",
                "testimonial": "<p>Arrived fast and beautifully boxed. They even let me model on their site :)</p>",
                "image": "shopify://shop_images/Screen_Shot_2020-10-09_at_12.17.44_PM.png",
                "author": "Sharon S.",
                "author_info": "New Orleans, LA"
              }
            }
          },
          "block_order": [
            "1624286980ca6c2f1b-3",
            "1624286980ca6c2f1b-1",
            "1624286980ca6c2f1b-2",
            "1624286980ca6c2f1b-0",
            "1624286980ca6c2f1b-4"
          ],
          "settings": {
            "title": "Don't take our word for it",
            "align_text": "center",
            "round_images": true,
            "color_scheme": "none"
          }
        },
        "list-collections-template": {
          "type": "list-collections-template",
          "settings": {
            "title_enable": false,
            "display_type": "all",
            "sort": "alphabetical"
          }
        },
        "162445498239d2eda9": {
          "type": "text-and-image",
          "settings": {
            "image": "shopify://shop_images/500kr_1800x1800_ce0ad400-6ec9-44ce-bd9e-014290fc239f.jpg",
            "subtitle": "",
            "title": "Gift cards now available",
            "text": "<p>We believe that local businesses are an integral part of a neighbourhood's character. Help us through difficult times and support us by buying a gift card.</p>",
            "button_label": "Shop gift cards",
            "button_link": "shopify://collections/gift-certificates",
            "image_width": "500",
            "layout": "left",
            "color_scheme": "none",
            "divider": false
          }
        },
        "age-verification-popup": {
          "type": "age-verification-popup",
          "settings": {
            "enable_test_mode": false,
            "color_scheme": "3",
            "image": "shopify://shop_images/200930Everleaf_FlatLay-FOREST_0844x5_900x_4d766fdf-51cf-4ad1-bd17-4805f4000a3a.webp",
            "blur_image": true,
            "heading": "Confirm your age",
            "text": "<p>Are you 18 years old or older?</p>",
            "decline_button_label": "No I'm not",
            "approve_button_label": "Yes I am",
            "decline_heading": "Come back when you're older",
            "decline_text": "<p>Sorry, the content of this store can't be seen by a younger audience. Come back when you're older.</p>",
            "return_button_label": "Oops, I entered incorrectly"
          }
        }
      },
      "content_for_index": [
        "161789469679fffe6b",
        "1525295772132",
        "162404838555055802",
        "162396200786f8a51c",
        "1499789718857",
        "1624029189039bf48d",
        "162402863370f49f13",
        "1624286980ca6c2f1b",
        "16166970499d200586",
        "16154004051a1fa86e",
        "16167068142513abfa",
        "162445498239d2eda9",
        "161789690258ed465f",
        "*************"
      ]
    }
  }
}
